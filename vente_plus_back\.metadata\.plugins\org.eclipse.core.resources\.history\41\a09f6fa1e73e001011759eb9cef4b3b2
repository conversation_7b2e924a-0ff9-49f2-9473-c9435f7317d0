package org.ms.categorie_service.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;

@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {

  @Override
  protected void configure(HttpSecurity http) throws Exception {
    http.csrf().disable()
      .authorizeRequests()
      .anyRequest().authenticated()  // Toutes les requêtes doivent être authentifiées
      .and()
      .oauth2ResourceServer().jwt(); // Spring valide automatiquement le JWT
  }
}
