# Complete Frontend-Backend Integration Test
Write-Host "=== FRONTEND-<PERSON>CKEND INTEGRATION VERIFICATION ===" -ForegroundColor Magenta

function Test-Endpoint {
    param(
        [string]$Method = "GET",
        [string]$Url,
        [string]$Description,
        [string]$Body = $null,
        [bool]$ShowResult = $true
    )
    
    Write-Host "`n--- Testing: $Description ---" -ForegroundColor Yellow
    Write-Host "$Method $Url" -ForegroundColor Cyan
    
    try {
        if ($Body) {
            $result = Invoke-RestMethod -Uri $Url -Method $Method -ContentType "application/json" -Body $Body
        } else {
            $result = Invoke-RestMethod -Uri $Url -Method $Method
        }
        
        Write-Host "✅ SUCCESS" -ForegroundColor Green
        if ($ShowResult) {
            if ($result -is [array] -and $result.Count -gt 0) {
                Write-Host "Count: $($result.Count)" -ForegroundColor White
            } elseif ($result -is [object] -and $result.PSObject.Properties.Count -gt 0) {
                Write-Host "Result: $($result | ConvertTo-Json -Compress -Depth 1)" -ForegroundColor White
            } else {
                Write-Host "Result: $result" -ForegroundColor White
            }
        }
        return $true
    } catch {
        Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test counters
$totalTests = 0
$passedTests = 0

Write-Host "`n🏥 STEP 1: CORE SERVICES HEALTH" -ForegroundColor Blue
$services = @(
    @{Name="Gateway"; Url="http://localhost:8888/actuator/health"},
    @{Name="Client Service"; Url="http://localhost:8084/actuator/health"},
    @{Name="Product Service"; Url="http://localhost:8082/actuator/health"},
    @{Name="Facture Service"; Url="http://localhost:8083/actuator/health"},
    @{Name="Reglement Service"; Url="http://localhost:8086/actuator/health"},
    @{Name="Dashboard Service"; Url="http://localhost:8089/actuator/health"}
)

foreach ($service in $services) {
    $totalTests++
    if (Test-Endpoint -Url $service.Url -Description "$($service.Name) Health Check" -ShowResult $false) {
        $passedTests++
    }
}

Write-Host "`n👥 STEP 2: CLIENT SERVICE ENDPOINTS (Frontend Integration)" -ForegroundColor Blue

# Test client endpoints that frontend uses
$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/clients" -Description "Get All Clients (via Gateway)") {
    $passedTests++
}

Write-Host "`n📦 STEP 3: PRODUCT SERVICE ENDPOINTS (Frontend Integration)" -ForegroundColor Blue

# Test product endpoints that frontend uses
$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/produits" -Description "Get All Products (via Gateway)") {
    $passedTests++
}

# Test stock management endpoint
$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/produits/1/stock" -Description "Get Product Stock (via Gateway)") {
    $passedTests++
}

Write-Host "`n🧾 STEP 4: FACTURE SERVICE ENDPOINTS (Frontend Integration)" -ForegroundColor Blue

# Test facture endpoints that frontend uses
$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/factures" -Description "Get All Factures (via Gateway)") {
    $passedTests++
}

Write-Host "`n💰 STEP 5: REGLEMENT SERVICE ENDPOINTS (Frontend Integration)" -ForegroundColor Blue

# Test reglement endpoints that frontend uses
$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/reglements" -Description "Get All Reglements (via Gateway)") {
    $passedTests++
}

$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/reglements/client/1" -Description "Get Client Reglements (via Gateway)") {
    $passedTests++
}

$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/reglements/total/client/1" -Description "Get Total Client Reglements (via Gateway)") {
    $passedTests++
}

$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/reglements/factures/non-reglees" -Description "Get Unpaid Invoices (via Gateway)") {
    $passedTests++
}

$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/reglements/factures/reglees" -Description "Get Paid Invoices (via Gateway)") {
    $passedTests++
}

$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/reglements/factures/partiellement-reglees" -Description "Get Partial Invoices (via Gateway)") {
    $passedTests++
}

Write-Host "`n📊 STEP 6: DASHBOARD SERVICE ENDPOINTS (Frontend Integration)" -ForegroundColor Blue

# Test dashboard endpoints that frontend uses
$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/dashboard/clients/analytics" -Description "Get Client Analytics (via Gateway)") {
    $passedTests++
}

$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/dashboard/produits/plus-vendus?limit=10" -Description "Get Best Products (via Gateway)") {
    $passedTests++
}

$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/dashboard/produits/rupture-stock" -Description "Get Out of Stock (via Gateway)") {
    $passedTests++
}

$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/dashboard/factures/reglees" -Description "Get Paid Invoices Dashboard (via Gateway)") {
    $passedTests++
}

$totalTests++
if (Test-Endpoint -Url "http://localhost:8888/dashboard/dettes" -Description "Get Client Debts (via Gateway)") {
    $passedTests++
}

Write-Host "`n🔄 STEP 7: STOCK MANAGEMENT INTEGRATION TEST" -ForegroundColor Blue

# Test the complete stock management flow
Write-Host "`nTesting Stock Management Flow..." -ForegroundColor Cyan

# Get current stock
try {
    $currentStock = Invoke-RestMethod -Uri "http://localhost:8888/produits/1/stock"
    Write-Host "Current stock for product 1: $currentStock" -ForegroundColor White
    
    # Check if there are any unpaid invoices that could be paid
    $unpaidInvoices = Invoke-RestMethod -Uri "http://localhost:8888/reglements/factures/non-reglees"
    if ($unpaidInvoices.Count -gt 0) {
        Write-Host "Found $($unpaidInvoices.Count) unpaid invoices - stock management ready" -ForegroundColor Green
    } else {
        Write-Host "No unpaid invoices found - create test data to verify stock management" -ForegroundColor Yellow
    }
    
    $totalTests++
    $passedTests++
} catch {
    Write-Host "❌ Stock management test failed: $($_.Exception.Message)" -ForegroundColor Red
    $totalTests++
}

Write-Host "`n📈 FINAL INTEGRATION RESULTS" -ForegroundColor Magenta
Write-Host "===========================================" -ForegroundColor White
Write-Host "Total Integration Tests: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $($totalTests - $passedTests)" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($passedTests / $totalTests) * 100, 2))%" -ForegroundColor Yellow

if ($passedTests -eq $totalTests) {
    Write-Host "`nFRONTEND-BACKEND INTEGRATION: PERFECT!" -ForegroundColor Green
    Write-Host "✅ All services accessible via Gateway" -ForegroundColor Green
    Write-Host "✅ All frontend endpoints working" -ForegroundColor Green
    Write-Host "✅ Stock management integrated" -ForegroundColor Green
    Write-Host "✅ Payment system functional" -ForegroundColor Green
    Write-Host "✅ Dashboard analytics working" -ForegroundColor Green
    Write-Host "`n🚀 FRONTEND IS READY TO LAUNCH!" -ForegroundColor Magenta
} else {
    Write-Host "`n⚠️ Some integration issues found" -ForegroundColor Yellow
    Write-Host "Check failed endpoints and fix before frontend launch" -ForegroundColor Yellow
}

Write-Host "`n🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Run: cd vente_plus_back/vente-plus && npm install" -ForegroundColor White
Write-Host "2. Run: ng serve" -ForegroundColor White
Write-Host "3. Access: http://localhost:4200" -ForegroundColor White
Write-Host "4. Test all frontend features!" -ForegroundColor White
