package org.ms.dashbordservice.services;

import lombok.extern.slf4j.Slf4j;
import org.ms.dashbordservice.dto.*;
import org.ms.dashbordservice.feign.ClientServiceClient;
import org.ms.dashbordservice.feign.FactureServiceClient;
import org.ms.dashbordservice.feign.ProduitServiceClient;
import org.ms.dashbordservice.feign.ReglementServiceClient;
import org.ms.dashbordservice.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DashboardServiceImpl implements DashboardService {

    @Autowired
    private ClientServiceClient clientServiceClient;
    
    @Autowired
    private FactureServiceClient factureServiceClient;
    
    @Autowired
    private ProduitServiceClient produitServiceClient;
    
    @Autowired
    private ReglementServiceClient reglementServiceClient;

    @Override
    public ClientAnalyticsDTO getClientAnalytics(Long clientId) {
        log.info("Génération des analytics pour le client: {}", clientId);
        
        Client client = clientServiceClient.getClientById(clientId);
        if (client == null) {
            throw new RuntimeException("Client introuvable avec l'ID: " + clientId);
        }
        
        ClientAnalyticsDTO analytics = new ClientAnalyticsDTO();
        analytics.setClientId(clientId);
        analytics.setNomClient(client.getNom());
        analytics.setPrenomClient(client.getPrenom());
        analytics.setEmailClient(client.getEmail());
        
        // Récupérer toutes les factures du client
        List<Facture> facturesClient = factureServiceClient.getAllFactures()
                .stream()
                .filter(f -> f.getClientID().equals(clientId))
                .collect(Collectors.toList());
        
        // Calculer le chiffre d'affaires total
        BigDecimal chiffresAffairesTotal = facturesClient.stream()
                .map(Facture::getMontantTTC)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        analytics.setChiffresAffairesTotal(chiffresAffairesTotal);
        
        // Calculer le chiffre d'affaires par année
        Map<Integer, BigDecimal> chiffresAffairesParAnnee = facturesClient.stream()
                .collect(Collectors.groupingBy(
                    f -> f.getDateFacture().toInstant().atZone(ZoneId.systemDefault()).getYear(),
                    Collectors.reducing(BigDecimal.ZERO, Facture::getMontantTTC, BigDecimal::add)
                ));
        analytics.setChiffresAffairesParAnnee(chiffresAffairesParAnnee);
        
        // Calculer le montant non payé
        BigDecimal montantNonPaye = facturesClient.stream()
                .map(f -> reglementServiceClient.getMontantRestantFacture(f.getId()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        analytics.setMontantNonPaye(montantNonPaye);
        
        // Factures réglées et non réglées
        List<FactureStatusDTO> facturesReglees = new ArrayList<>();
        List<FactureStatusDTO> facturesNonReglees = new ArrayList<>();
        
        for (Facture facture : facturesClient) {
            FactureStatusDTO factureStatus = createFactureStatusDTO(facture, client);
            if (factureStatus.isEstCompletelyPayee()) {
                facturesReglees.add(factureStatus);
            } else {
                facturesNonReglees.add(factureStatus);
            }
        }
        
        analytics.setFacturesReglees(facturesReglees);
        analytics.setFacturesNonReglees(facturesNonReglees);
        
        // Produits les plus sollicités par ce client
        analytics.setProduitsLesPlusSollicites(getProduitsPlusSollicitesParClient(clientId));
        
        // Statistiques de fidélité
        analytics.setNombreCommandes(facturesClient.size());
        if (!facturesClient.isEmpty()) {
            BigDecimal montantMoyen = chiffresAffairesTotal.divide(
                BigDecimal.valueOf(facturesClient.size()), 2, RoundingMode.HALF_UP);
            analytics.setMontantMoyenCommande(montantMoyen);
        } else {
            analytics.setMontantMoyenCommande(BigDecimal.ZERO);
        }
        
        return analytics;
    }

    @Override
    public List<ClientAnalyticsDTO> getAllClientsAnalytics() {
        log.info("Génération des analytics pour tous les clients");
        List<Client> clients = clientServiceClient.getAllClients();
        return clients.stream()
                .map(client -> getClientAnalytics(client.getId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<ClientFideliteDTO> getClientsPlusFideles(int limit) {
        log.info("Récupération des {} clients les plus fidèles", limit);
        
        List<Client> clients = clientServiceClient.getAllClients();
        List<ClientFideliteDTO> clientsFideles = new ArrayList<>();
        
        for (Client client : clients) {
            List<Facture> facturesClient = factureServiceClient.getAllFactures()
                    .stream()
                    .filter(f -> f.getClientID().equals(client.getId()))
                    .collect(Collectors.toList());
            
            if (!facturesClient.isEmpty()) {
                ClientFideliteDTO fidelite = new ClientFideliteDTO();
                fidelite.setClientId(client.getId());
                fidelite.setNomClient(client.getNom());
                fidelite.setPrenomClient(client.getPrenom());
                fidelite.setEmailClient(client.getEmail());
                fidelite.setNombreCommandes(facturesClient.size());
                
                BigDecimal chiffresAffaires = facturesClient.stream()
                        .map(Facture::getMontantTTC)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                fidelite.setChiffresAffairesTotal(chiffresAffaires);
                
                BigDecimal montantMoyen = chiffresAffaires.divide(
                    BigDecimal.valueOf(facturesClient.size()), 2, RoundingMode.HALF_UP);
                fidelite.setMontantMoyenCommande(montantMoyen);
                
                // Dates première et dernière commande
                Optional<Date> premiereCommande = facturesClient.stream()
                        .map(Facture::getDateFacture)
                        .min(Date::compareTo);
                Optional<Date> derniereCommande = facturesClient.stream()
                        .map(Facture::getDateFacture)
                        .max(Date::compareTo);
                
                if (premiereCommande.isPresent()) {
                    fidelite.setDatePremiereCommande(
                        premiereCommande.get().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                }
                if (derniereCommande.isPresent()) {
                    fidelite.setDateDerniereCommande(
                        derniereCommande.get().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                }
                
                // Score de fidélité (basé sur nombre de commandes et montant total)
                double scoreFidelite = (facturesClient.size() * 0.4) + 
                                     (chiffresAffaires.doubleValue() / 1000 * 0.6);
                fidelite.setScoreFidelite(scoreFidelite);
                
                clientsFideles.add(fidelite);
            }
        }
        
        return clientsFideles.stream()
                .sorted((c1, c2) -> Double.compare(c2.getScoreFidelite(), c1.getScoreFidelite()))
                .limit(limit)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProduitPopulariteDTO> getProduitsPlusVendus(int limit) {
        log.info("Récupération des {} produits les plus vendus", limit);
        return getProduitsPlusVendusInternal(null, limit);
    }

    @Override
    public List<ProduitPopulariteDTO> getProduitsPlusVendusParAnnee(int annee, int limit) {
        log.info("Récupération des {} produits les plus vendus pour l'année {}", limit, annee);
        return getProduitsPlusVendusInternal(annee, limit);
    }

    private List<ProduitPopulariteDTO> getProduitsPlusVendusInternal(Integer annee, int limit) {
        List<Facture> factures = factureServiceClient.getAllFactures();
        
        if (annee != null) {
            factures = factures.stream()
                    .filter(f -> f.getDateFacture().toInstant().atZone(ZoneId.systemDefault()).getYear() == annee)
                    .collect(Collectors.toList());
        }
        
        Map<Long, ProduitPopulariteDTO> produitsStats = new HashMap<>();
        
        for (Facture facture : factures) {
            if (facture.getFacturelignes() != null) {
                for (FactureLigne ligne : facture.getFacturelignes()) {
                    Long produitId = ligne.getProduitID();
                    
                    ProduitPopulariteDTO stats = produitsStats.computeIfAbsent(produitId, id -> {
                        Produit produit = produitServiceClient.getProduitById(id);
                        ProduitPopulariteDTO dto = new ProduitPopulariteDTO();
                        dto.setProduitId(id);
                        if (produit != null) {
                            dto.setNomProduit(produit.getNom());
                            dto.setReferenceProduit(produit.getReference());
                        }
                        dto.setQuantiteVendue(0);
                        dto.setChiffresAffaires(BigDecimal.ZERO);
                        dto.setNombreCommandes(0);
                        return dto;
                    });
                    
                    stats.setQuantiteVendue(stats.getQuantiteVendue() + ligne.getQuantite());
                    BigDecimal montantLigne = ligne.getPrix().multiply(BigDecimal.valueOf(ligne.getQuantite()));
                    stats.setChiffresAffaires(stats.getChiffresAffaires().add(montantLigne));
                    stats.setNombreCommandes(stats.getNombreCommandes() + 1);
                }
            }
        }
        
        return produitsStats.values().stream()
                .sorted((p1, p2) -> Long.compare(p2.getQuantiteVendue(), p1.getQuantiteVendue()))
                .limit(limit)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProduitStockDTO> getProduitsEnRuptureStock() {
        log.info("Récupération des produits en rupture de stock");

        List<Produit> produits = produitServiceClient.getAllProduits();
        return produits.stream()
                .filter(p -> p.getQuantite() <= 0)
                .map(this::createProduitStockDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<FactureStatusDTO> getFacturesReglees() {
        log.info("Récupération des factures réglées");

        List<Facture> factures = factureServiceClient.getAllFactures();
        List<FactureStatusDTO> facturesReglees = new ArrayList<>();

        for (Facture facture : factures) {
            Boolean estPayee = reglementServiceClient.isFactureCompletelyPayee(facture.getId());
            if (estPayee != null && estPayee) {
                Client client = clientServiceClient.getClientById(facture.getClientID());
                facturesReglees.add(createFactureStatusDTO(facture, client));
            }
        }

        return facturesReglees;
    }

    @Override
    public List<FactureStatusDTO> getFacturesNonReglees() {
        log.info("Récupération des factures non réglées");

        List<Long> facturesNonRegleesIds = reglementServiceClient.getFacturesNonReglees();
        List<FactureStatusDTO> facturesNonReglees = new ArrayList<>();

        for (Long factureId : facturesNonRegleesIds) {
            Facture facture = factureServiceClient.getFactureById(factureId);
            if (facture != null) {
                Client client = clientServiceClient.getClientById(facture.getClientID());
                facturesNonReglees.add(createFactureStatusDTO(facture, client));
            }
        }

        return facturesNonReglees;
    }

    @Override
    public List<FactureStatusDTO> getFacturesPartiellemementReglees() {
        log.info("Récupération des factures partiellement réglées");

        List<Long> facturesPartiellesIds = reglementServiceClient.getFacturesPartiellemementReglees();
        List<FactureStatusDTO> facturesPartielles = new ArrayList<>();

        for (Long factureId : facturesPartiellesIds) {
            Facture facture = factureServiceClient.getFactureById(factureId);
            if (facture != null) {
                Client client = clientServiceClient.getClientById(facture.getClientID());
                facturesPartielles.add(createFactureStatusDTO(facture, client));
            }
        }

        return facturesPartielles;
    }

    @Override
    public List<DetteClientDTO> getDettesByClient() {
        log.info("Récupération des dettes par client");

        List<Client> clients = clientServiceClient.getAllClients();
        List<DetteClientDTO> dettes = new ArrayList<>();

        for (Client client : clients) {
            DetteClientDTO dette = getDetteByClient(client.getId());
            if (dette.getMontantTotalDette().compareTo(BigDecimal.ZERO) > 0) {
                dettes.add(dette);
            }
        }

        return dettes.stream()
                .sorted((d1, d2) -> d2.getMontantTotalDette().compareTo(d1.getMontantTotalDette()))
                .collect(Collectors.toList());
    }

    @Override
    public DetteClientDTO getDetteByClient(Long clientId) {
        log.info("Récupération des dettes pour le client: {}", clientId);

        Client client = clientServiceClient.getClientById(clientId);
        if (client == null) {
            throw new RuntimeException("Client introuvable avec l'ID: " + clientId);
        }

        DetteClientDTO dette = new DetteClientDTO();
        dette.setClientId(clientId);
        dette.setNomClient(client.getNom());
        dette.setPrenomClient(client.getPrenom());
        dette.setEmailClient(client.getEmail());

        // Récupérer toutes les factures du client
        List<Facture> facturesClient = factureServiceClient.getAllFactures()
                .stream()
                .filter(f -> f.getClientID().equals(clientId))
                .collect(Collectors.toList());

        List<FactureStatusDTO> facturesImpayees = new ArrayList<>();
        BigDecimal montantTotalDette = BigDecimal.ZERO;

        for (Facture facture : facturesClient) {
            BigDecimal montantRestant = reglementServiceClient.getMontantRestantFacture(facture.getId());
            if (montantRestant.compareTo(BigDecimal.ZERO) > 0) {
                FactureStatusDTO factureStatus = createFactureStatusDTO(facture, client);
                facturesImpayees.add(factureStatus);
                montantTotalDette = montantTotalDette.add(montantRestant);
            }
        }

        dette.setFacturesImpayees(facturesImpayees);
        dette.setNombreFacturesImpayees(facturesImpayees.size());
        dette.setMontantTotalDette(montantTotalDette);

        return dette;
    }

    // Méthodes utilitaires privées
    private FactureStatusDTO createFactureStatusDTO(Facture facture, Client client) {
        FactureStatusDTO dto = new FactureStatusDTO();
        dto.setFactureId(facture.getId());
        dto.setDateFacture(facture.getDateFacture());
        dto.setMontantTTC(facture.getMontantTTC());

        BigDecimal montantPaye = reglementServiceClient.getTotalReglementsByFacture(facture.getId());
        BigDecimal montantRestant = reglementServiceClient.getMontantRestantFacture(facture.getId());

        dto.setMontantPaye(montantPaye);
        dto.setMontantRestant(montantRestant);
        dto.setEstCompletelyPayee(montantRestant.compareTo(BigDecimal.ZERO) <= 0);
        dto.setClientId(facture.getClientID());

        if (client != null) {
            dto.setNomClient(client.getNom() + " " + client.getPrenom());
        }

        return dto;
    }

    private ProduitStockDTO createProduitStockDTO(Produit produit) {
        ProduitStockDTO dto = new ProduitStockDTO();
        dto.setProduitId(produit.getId());
        dto.setNomProduit(produit.getNom());
        dto.setReferenceProduit(produit.getReference());
        dto.setQuantiteEnStock(produit.getQuantite());
        dto.setPrix(produit.getPrix());
        dto.setEnRuptureStock(produit.getQuantite() <= 0);

        if (produit.getCategorie() != null) {
            dto.setNomCategorie(produit.getCategorie().getNom());
        }

        return dto;
    }

    private List<ProduitPopulariteDTO> getProduitsPlusSollicitesParClient(Long clientId) {
        List<Facture> facturesClient = factureServiceClient.getAllFactures()
                .stream()
                .filter(f -> f.getClientID().equals(clientId))
                .collect(Collectors.toList());

        Map<Long, ProduitPopulariteDTO> produitsStats = new HashMap<>();

        for (Facture facture : facturesClient) {
            if (facture.getFacturelignes() != null) {
                for (FactureLigne ligne : facture.getFacturelignes()) {
                    Long produitId = ligne.getProduitID();

                    ProduitPopulariteDTO stats = produitsStats.computeIfAbsent(produitId, id -> {
                        Produit produit = produitServiceClient.getProduitById(id);
                        ProduitPopulariteDTO dto = new ProduitPopulariteDTO();
                        dto.setProduitId(id);
                        if (produit != null) {
                            dto.setNomProduit(produit.getNom());
                            dto.setReferenceProduit(produit.getReference());
                        }
                        dto.setQuantiteVendue(0);
                        dto.setChiffresAffaires(BigDecimal.ZERO);
                        dto.setNombreCommandes(0);
                        return dto;
                    });

                    stats.setQuantiteVendue(stats.getQuantiteVendue() + ligne.getQuantite());
                    BigDecimal montantLigne = ligne.getPrix().multiply(BigDecimal.valueOf(ligne.getQuantite()));
                    stats.setChiffresAffaires(stats.getChiffresAffaires().add(montantLigne));
                    stats.setNombreCommandes(stats.getNombreCommandes() + 1);
                }
            }
        }

        return produitsStats.values().stream()
                .sorted((p1, p2) -> Long.compare(p2.getQuantiteVendue(), p1.getQuantiteVendue()))
                .limit(10) // Top 10 pour un client
                .collect(Collectors.toList());
    }
}
