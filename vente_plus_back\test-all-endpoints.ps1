# Comprehensive Test for ALL Reglement and Dashboard Endpoints
Write-Host "=== COMPREHENSIVE ENDPOINT VERIFICATION ===" -ForegroundColor Magenta

function Test-Endpoint {
    param(
        [string]$Method = "GET",
        [string]$Url,
        [string]$Description,
        [string]$Body = $null,
        [bool]$ShowResult = $true
    )
    
    Write-Host "`n--- Testing: $Description ---" -ForegroundColor Yellow
    Write-Host "$Method $Url" -ForegroundColor Cyan
    
    try {
        if ($Body) {
            $result = Invoke-RestMethod -Uri $Url -Method $Method -ContentType "application/json" -Body $Body
        } else {
            $result = Invoke-RestMethod -Uri $Url -Method $Method
        }
        
        Write-Host "✅ SUCCESS" -ForegroundColor Green
        if ($ShowResult) {
            if ($result -is [array] -and $result.Count -gt 0) {
                Write-Host "Count: $($result.Count)" -ForegroundColor White
            } elseif ($result -is [object] -and $result.PSObject.Properties.Count -gt 0) {
                Write-Host "Result: $($result | ConvertTo-Json -Compress -Depth 1)" -ForegroundColor White
            } else {
                Write-Host "Result: $result" -ForegroundColor White
            }
        }
        return $true
    } catch {
        Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test counters
$totalTests = 0
$passedTests = 0

Write-Host "`nSTEP 1: SERVICE HEALTH CHECKS" -ForegroundColor Blue
$services = @(
    @{Name="Reglement Service"; Url="http://localhost:8086/actuator/health"},
    @{Name="Dashboard Service"; Url="http://localhost:8089/actuator/health"}
)

foreach ($service in $services) {
    $totalTests++
    if (Test-Endpoint -Url $service.Url -Description "$($service.Name) Health Check" -ShowResult $false) {
        $passedTests++
    }
}

Write-Host "`nSTEP 2: REGLEMENT SERVICE ENDPOINTS" -ForegroundColor Blue

# Get all reglements
$totalTests++
if (Test-Endpoint -Url "http://localhost:8086/reglements" -Description "Get All Reglements") {
    $passedTests++
}

# Get reglements by client
$totalTests++
if (Test-Endpoint -Url "http://localhost:8086/reglements/client/1" -Description "Get Reglements by Client 1") {
    $passedTests++
}

# Get total reglements by client
$totalTests++
if (Test-Endpoint -Url "http://localhost:8086/reglements/total/client/1" -Description "Get Total Reglements by Client 1") {
    $passedTests++
}

# Get reglements by invoice
$totalTests++
if (Test-Endpoint -Url "http://localhost:8086/reglements/facture/1" -Description "Get Reglements by Invoice 1") {
    $passedTests++
}

# Get remaining amount for invoice
$totalTests++
if (Test-Endpoint -Url "http://localhost:8086/reglements/restant/facture/1" -Description "Get Remaining Amount for Invoice 1") {
    $passedTests++
}

# Get reglements by payment mode
$totalTests++
if (Test-Endpoint -Url "http://localhost:8086/reglements/mode/VIREMENT" -Description "Get Reglements by VIREMENT Mode") {
    $passedTests++
}

# Get reglements by year
$totalTests++
if (Test-Endpoint -Url "http://localhost:8086/reglements/annee/2025" -Description "Get Reglements by Year 2025") {
    $passedTests++
}

# Get unpaid invoices
$totalTests++
if (Test-Endpoint -Url "http://localhost:8086/reglements/factures/non-reglees" -Description "Get Unpaid Invoices") {
    $passedTests++
}

# Get paid invoices
$totalTests++
if (Test-Endpoint -Url "http://localhost:8086/reglements/factures/reglees" -Description "Get Paid Invoices") {
    $passedTests++
}

# Get partially paid invoices
$totalTests++
if (Test-Endpoint -Url "http://localhost:8086/reglements/factures/partiellement-reglees" -Description "Get Partially Paid Invoices") {
    $passedTests++
}

Write-Host "`nSTEP 3: DASHBOARD CLIENT ANALYTICS" -ForegroundColor Blue

# All clients analytics
$totalTests++
if (Test-Endpoint -Url "http://localhost:8089/dashboard/clients/analytics" -Description "Get All Clients Analytics") {
    $passedTests++
}

# Specific client analytics
$totalTests++
if (Test-Endpoint -Url "http://localhost:8089/dashboard/client/1/analytics" -Description "Get Client 1 Analytics") {
    $passedTests++
}

# Client revenue
$totalTests++
if (Test-Endpoint -Url "http://localhost:8089/dashboard/client/1/chiffres-affaires" -Description "Get Client 1 Revenue") {
    $passedTests++
}

# Most loyal clients
$totalTests++
if (Test-Endpoint -Url "http://localhost:8089/dashboard/clients/fideles?limit=5" -Description "Get Most Loyal Clients") {
    $passedTests++
}

Write-Host "`nSTEP 4: DASHBOARD PRODUCT ANALYTICS" -ForegroundColor Blue

# Best selling products
$totalTests++
if (Test-Endpoint -Url "http://localhost:8089/dashboard/produits/plus-vendus?limit=10" -Description "Get Best Selling Products") {
    $passedTests++
}

# Best selling products by year
$totalTests++
if (Test-Endpoint -Url "http://localhost:8089/dashboard/produits/plus-vendus/annee/2025?limit=10" -Description "Get Best Selling Products 2025") {
    $passedTests++
}

# Out of stock products
$totalTests++
if (Test-Endpoint -Url "http://localhost:8089/dashboard/produits/rupture-stock" -Description "Get Out of Stock Products") {
    $passedTests++
}

Write-Host "`nSTEP 5: DASHBOARD INVOICE ANALYTICS" -ForegroundColor Blue

# Paid invoices
$totalTests++
if (Test-Endpoint -Url "http://localhost:8089/dashboard/factures/reglees" -Description "Get Paid Invoices Dashboard") {
    $passedTests++
}

# Unpaid invoices
$totalTests++
if (Test-Endpoint -Url "http://localhost:8089/dashboard/factures/non-reglees" -Description "Get Unpaid Invoices Dashboard") {
    $passedTests++
}

# Partially paid invoices
$totalTests++
if (Test-Endpoint -Url "http://localhost:8089/dashboard/factures/partiellement-reglees" -Description "Get Partially Paid Invoices Dashboard") {
    $passedTests++
}

Write-Host "`nSTEP 6: DASHBOARD DEBT MANAGEMENT" -ForegroundColor Blue

# All client debts
$totalTests++
if (Test-Endpoint -Url "http://localhost:8089/dashboard/dettes" -Description "Get All Client Debts") {
    $passedTests++
}

# Specific client debt
$totalTests++
if (Test-Endpoint -Url "http://localhost:8089/dashboard/client/1/dettes" -Description "Get Client 1 Debt") {
    $passedTests++
}

# Client 2 debt (should have debt)
$totalTests++
if (Test-Endpoint -Url "http://localhost:8089/dashboard/client/2/dettes" -Description "Get Client 2 Debt") {
    $passedTests++
}

Write-Host "`nSTEP 7: STOCK MANAGEMENT VERIFICATION" -ForegroundColor Blue

# Check current stock
$totalTests++
if (Test-Endpoint -Url "http://localhost:8082/produits/1/stock" -Description "Get Product 1 Current Stock") {
    $passedTests++
}

Write-Host "`nFINAL RESULTS" -ForegroundColor Magenta
Write-Host "===========================================" -ForegroundColor White
Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $($totalTests - $passedTests)" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($passedTests / $totalTests) * 100, 2))%" -ForegroundColor Yellow

if ($passedTests -eq $totalTests) {
    Write-Host "`nALL ENDPOINTS WORKING PERFECTLY!" -ForegroundColor Green
    Write-Host "Reglement Service: FULLY FUNCTIONAL" -ForegroundColor Green
    Write-Host "Dashboard Service: FULLY FUNCTIONAL" -ForegroundColor Green
    Write-Host "Stock Management: WORKING" -ForegroundColor Green
    Write-Host "Inter-service Communication: EXCELLENT" -ForegroundColor Green
} else {
    Write-Host "`nSome endpoints need attention" -ForegroundColor Yellow
}

Write-Host "`nSystem Status: PRODUCTION READY!" -ForegroundColor Magenta
