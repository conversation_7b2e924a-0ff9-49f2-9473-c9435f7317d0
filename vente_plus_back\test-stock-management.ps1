# Test Stock Management Logic
Write-Host "=== Testing Stock Management Logic ===" -ForegroundColor Green

function Test-Endpoint {
    param(
        [string]$Method = "GET",
        [string]$Url,
        [string]$Description,
        [string]$Body = $null
    )
    
    Write-Host "`n--- Testing: $Description ---" -ForegroundColor Yellow
    Write-Host "$Method $Url" -ForegroundColor Cyan
    
    try {
        if ($Body) {
            $result = Invoke-RestMethod -Uri $Url -Method $Method -ContentType "application/json" -Body $Body
        } else {
            $result = Invoke-RestMethod -Uri $Url -Method $Method
        }
        
        Write-Host "✅ Success:" -ForegroundColor Green
        $result | ConvertTo-Json -Depth 3
        return $result
    } catch {
        Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Step 1: Create a category first
Write-Host "`n🏷️ Creating Category..." -ForegroundColor Blue
$boundary = [System.Guid]::NewGuid().ToString()
$LF = "`r`n"
$bodyLines = (
    "--$boundary",
    "Content-Disposition: form-data; name=`"categorie`"",
    "Content-Type: application/json$LF",
    '{"nom": "Test Category", "description": "Category for testing"}',
    "--$boundary--$LF"
) -join $LF

try {
    $category = Invoke-RestMethod -Uri "http://localhost:8087/categories" -Method POST -ContentType "multipart/form-data; boundary=$boundary" -Body $bodyLines
    Write-Host "✅ Category created: $($category.nom)" -ForegroundColor Green
} catch {
    Write-Host "❌ Category creation failed, continuing with existing categories..." -ForegroundColor Yellow
}

# Step 2: Create a product with stock
Write-Host "`n📦 Creating Product with Stock..." -ForegroundColor Blue
$boundary = [System.Guid]::NewGuid().ToString()
$LF = "`r`n"
$bodyLines = (
    "--$boundary",
    "Content-Disposition: form-data; name=`"produit`"",
    "Content-Type: application/json$LF",
    '{"nom": "Test Product", "prix": 100.00, "quantite": 50, "reference": "TEST001", "categorieId": 1}',
    "--$boundary--$LF"
) -join $LF

$product = Invoke-RestMethod -Uri "http://localhost:8082/produits" -Method POST -ContentType "multipart/form-data; boundary=$boundary" -Body $bodyLines
Write-Host "✅ Product created with stock: $($product.quantite)" -ForegroundColor Green

# Step 3: Check initial stock
$initialStock = Test-Endpoint -Url "http://localhost:8082/produits/$($product.id)/stock" -Description "Check Initial Stock"

# Step 4: Create a devis
Write-Host "`n📋 Creating Devis..." -ForegroundColor Blue
$devisBody = @{
    clientId = 1
    dateValidite = "2024-12-31"
    lignes = @(
        @{
            produitId = $product.id
            quantite = 5
            prixUnitaire = 100.00
            remise = 0
            tva = 20
        }
    )
} | ConvertTo-Json -Depth 3

$devis = Test-Endpoint -Method "POST" -Url "http://localhost:8085/devis" -Description "Create Devis" -Body $devisBody

if ($devis) {
    # Step 5: Validate devis
    Test-Endpoint -Method "PUT" -Url "http://localhost:8085/devis/$($devis.id)/valider" -Description "Validate Devis"
    
    # Step 6: Create invoice from devis
    $facture = Test-Endpoint -Method "POST" -Url "http://localhost:8083/factures/from-devis/$($devis.id)" -Description "Create Invoice from Devis"
    
    if ($facture) {
        # Step 7: Check stock before payment (should be unchanged)
        $stockBeforePayment = Test-Endpoint -Url "http://localhost:8082/produits/$($product.id)/stock" -Description "Check Stock Before Payment"
        
        # Step 8: Create payment (partial)
        Write-Host "`n💰 Creating Partial Payment..." -ForegroundColor Blue
        $partialPaymentBody = @{
            factureId = $facture.id
            montantPaye = 60.00
            modeReglement = "VIREMENT"
            reference = "PARTIAL001"
            commentaire = "Partial payment test"
        } | ConvertTo-Json
        
        $partialPayment = Test-Endpoint -Method "POST" -Url "http://localhost:8086/reglements" -Description "Create Partial Payment" -Body $partialPaymentBody
        
        # Step 9: Check stock after partial payment (should be unchanged)
        $stockAfterPartial = Test-Endpoint -Url "http://localhost:8082/produits/$($product.id)/stock" -Description "Check Stock After Partial Payment"
        
        # Step 10: Create final payment to complete the invoice
        Write-Host "`n💰 Creating Final Payment..." -ForegroundColor Blue
        $finalPaymentBody = @{
            factureId = $facture.id
            montantPaye = 60.00
            modeReglement = "ESPECES"
            reference = "FINAL001"
            commentaire = "Final payment test"
        } | ConvertTo-Json
        
        $finalPayment = Test-Endpoint -Method "POST" -Url "http://localhost:8086/reglements" -Description "Create Final Payment" -Body $finalPaymentBody
        
        # Step 11: Check stock after full payment (should be reduced)
        $stockAfterFull = Test-Endpoint -Url "http://localhost:8082/produits/$($product.id)/stock" -Description "Check Stock After Full Payment"
        
        # Step 12: Summary
        Write-Host "`n📊 STOCK MANAGEMENT TEST SUMMARY" -ForegroundColor Magenta
        Write-Host "Initial Stock: $initialStock" -ForegroundColor White
        Write-Host "Stock Before Payment: $stockBeforePayment" -ForegroundColor White
        Write-Host "Stock After Partial Payment: $stockAfterPartial" -ForegroundColor White
        Write-Host "Stock After Full Payment: $stockAfterFull" -ForegroundColor White
        Write-Host "Expected Final Stock: $($initialStock - 5)" -ForegroundColor White
        
        if ($stockAfterFull -eq ($initialStock - 5)) {
            Write-Host "✅ STOCK MANAGEMENT WORKING CORRECTLY!" -ForegroundColor Green
        } else {
            Write-Host "❌ STOCK MANAGEMENT NOT WORKING AS EXPECTED!" -ForegroundColor Red
        }
    }
}

Write-Host "`n🎯 Test completed!" -ForegroundColor Green
