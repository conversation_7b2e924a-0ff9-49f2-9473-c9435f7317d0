import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FactureService } from '../../services/facture.service';
import { ReglementService } from '../../services/reglement.service';
import { Facture } from '../../models/facture.model';
import { Reglement, ReglementDTO } from '../../models/reglement.model';

@Component({
  selector: 'app-facture',
  templateUrl: './facture.component.html',
  styleUrls: ['./facture.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ]
})
export class FactureComponent implements OnInit {
  factures: Facture[] = [];
  selectedFacture: Facture | null = null;
  isLoading = false;
  showAlert = false;
  alertMessage = '';
  alertType = '';
  showViewModal = false;
  showDeleteModal = false;
  searchTerm = '';
  currentPage = 1;
  itemsPerPage = 10;
  showFilters = false;
  clientFilter = '';
  dateFilter = '';

  // Payment-related properties
  showPaymentModal = false;
  showPaymentHistoryModal = false;
  paymentData: ReglementDTO = {
    factureId: 0,
    montantPaye: 0,
    modeReglement: 'CARTE',
    reference: '',
    commentaire: ''
  };
  paymentHistory: Reglement[] = [];
  facturePayments: Map<number, Reglement[]> = new Map();
  isProcessingPayment = false;

  constructor(
    private factureService: FactureService,
    private reglementService: ReglementService
  ) {}

  ngOnInit(): void {
    this.loadFactures();
    this.loadAllPayments();
  }

  loadFactures(): void {
    this.isLoading = true;
    this.factureService.getFactures().subscribe({
      next: (data) => {
        this.factures = data;
        this.isLoading = false;
      },
      error: (error) => {
        this.showError('Erreur lors du chargement des factures');
        this.isLoading = false;
      }
    });
  }

  get paginatedFactures(): Facture[] {
    let filteredFactures = this.factures;

    // Appliquer le filtre de recherche
    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      filteredFactures = filteredFactures.filter(f => 
        f.id.toString().includes(searchLower) ||
        f.client?.nom.toLowerCase().includes(searchLower) ||
        f.client?.prenom.toLowerCase().includes(searchLower)
      );
    }

    // Appliquer le filtre client
    if (this.clientFilter) {
      const clientLower = this.clientFilter.toLowerCase();
      filteredFactures = filteredFactures.filter(f =>
        f.client?.nom.toLowerCase().includes(clientLower) ||
        f.client?.prenom.toLowerCase().includes(clientLower)
      );
    }

    // Appliquer le filtre de date
    if (this.dateFilter) {
      const filterDate = new Date(this.dateFilter).toISOString().split('T')[0];
      filteredFactures = filteredFactures.filter(f =>
        new Date(f.dateFacture).toISOString().split('T')[0] === filterDate
      );
    }

    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    return filteredFactures.slice(startIndex, startIndex + this.itemsPerPage);
  }

  get totalPages(): number {
    let filteredFactures = this.factures;

    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      filteredFactures = filteredFactures.filter(f => 
        f.id.toString().includes(searchLower) ||
        f.client?.nom.toLowerCase().includes(searchLower) ||
        f.client?.prenom.toLowerCase().includes(searchLower)
      );
    }

    if (this.clientFilter) {
      const clientLower = this.clientFilter.toLowerCase();
      filteredFactures = filteredFactures.filter(f =>
        f.client?.nom.toLowerCase().includes(clientLower) ||
        f.client?.prenom.toLowerCase().includes(clientLower)
      );
    }

    if (this.dateFilter) {
      const filterDate = new Date(this.dateFilter).toISOString().split('T')[0];
      filteredFactures = filteredFactures.filter(f =>
        new Date(f.dateFacture).toISOString().split('T')[0] === filterDate
      );
    }

    return Math.ceil(filteredFactures.length / this.itemsPerPage);
  }

  toggleFilters(): void {
    this.showFilters = !this.showFilters;
  }

  onClientFilterChange(value: string): void {
    this.clientFilter = value;
    this.currentPage = 1;
  }

  onDateFilterChange(value: string): void {
    this.dateFilter = value;
    this.currentPage = 1;
  }

  resetFilters(): void {
    this.clientFilter = '';
    this.dateFilter = '';
    this.currentPage = 1;
  }

  onSearchChange(term: string): void {
    this.searchTerm = term;
    this.currentPage = 1;
  }

  prevPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  goToPage(page: number): void {
    this.currentPage = page;
  }

  viewFacture(facture: Facture): void {
    this.selectedFacture = facture;
    this.showViewModal = true;
  }

  closeViewModal(): void {
    this.showViewModal = false;
    this.selectedFacture = null;
  }

  openDeleteModal(facture: Facture): void {
    this.selectedFacture = facture;
    this.showDeleteModal = true;
  }

  closeDeleteModal(): void {
    this.showDeleteModal = false;
    this.selectedFacture = null;
  }

  onDelete(): void {
    if (this.selectedFacture) {
      this.factureService.deleteFacture(this.selectedFacture.id).subscribe({
        next: () => {
          this.factures = this.factures.filter(f => f.id !== this.selectedFacture?.id);
          this.showSuccess('Facture supprimée avec succès');
          this.closeDeleteModal();
        },
        error: (error) => {
          this.showError('Erreur lors de la suppression de la facture');
        }
      });
    }
  }

  showSuccess(message: string): void {
    this.alertMessage = message;
    this.alertType = 'success';
    this.showAlert = true;
    setTimeout(() => this.closeAlert(), 3000);
  }

  showError(message: string): void {
    this.alertMessage = message;
    this.alertType = 'error';
    this.showAlert = true;
    setTimeout(() => this.closeAlert(), 3000);
  }

  closeAlert(): void {
    this.showAlert = false;
  }

  // ==================== PAYMENT METHODS ====================

  loadAllPayments(): void {
    this.reglementService.getAllReglements().subscribe({
      next: (payments) => {
        // Group payments by invoice ID
        this.facturePayments.clear();
        payments.forEach(payment => {
          if (!this.facturePayments.has(payment.factureId)) {
            this.facturePayments.set(payment.factureId, []);
          }
          this.facturePayments.get(payment.factureId)!.push(payment);
        });
      },
      error: (error) => {
        console.error('Error loading payments:', error);
      }
    });
  }

  openPaymentModal(facture: Facture): void {
    this.selectedFacture = facture;
    this.paymentData = {
      factureId: facture.id,
      montantPaye: this.getRemainingAmount(facture),
      modeReglement: 'CARTE',
      reference: '',
      commentaire: ''
    };
    this.showPaymentModal = true;
  }

  closePaymentModal(): void {
    this.showPaymentModal = false;
    this.selectedFacture = null;
    this.isProcessingPayment = false;
  }

  onPaymentSubmit(): void {
    if (!this.selectedFacture || this.isProcessingPayment) return;

    this.isProcessingPayment = true;
    this.reglementService.createReglement(this.paymentData).subscribe({
      next: (payment) => {
        this.showSuccess('Paiement effectué avec succès');
        this.loadAllPayments(); // Reload payments
        this.closePaymentModal();
      },
      error: (error) => {
        this.showError('Erreur lors du traitement du paiement');
        this.isProcessingPayment = false;
      }
    });
  }

  viewPaymentHistory(facture: Facture): void {
    this.selectedFacture = facture;
    this.paymentHistory = this.facturePayments.get(facture.id) || [];
    this.showPaymentHistoryModal = true;
  }

  closePaymentHistoryModal(): void {
    this.showPaymentHistoryModal = false;
    this.selectedFacture = null;
    this.paymentHistory = [];
  }

  // ==================== PAYMENT STATUS METHODS ====================

  getPaymentStatusText(facture: Facture): string {
    const amountPaid = this.getAmountPaid(facture);
    const total = facture.montantTTC;

    if (amountPaid === 0) {
      return 'Non payée';
    } else if (amountPaid >= total) {
      return 'Payée';
    } else {
      return 'Partiellement payée';
    }
  }

  getPaymentStatusClass(facture: Facture): string {
    const amountPaid = this.getAmountPaid(facture);
    const total = facture.montantTTC;

    if (amountPaid === 0) {
      return 'status-unpaid';
    } else if (amountPaid >= total) {
      return 'status-paid';
    } else {
      return 'status-partial';
    }
  }

  isFactureFullyPaid(facture: Facture): boolean {
    const amountPaid = this.getAmountPaid(facture);
    return amountPaid >= facture.montantTTC;
  }

  getAmountPaid(facture: Facture): number {
    const payments = this.facturePayments.get(facture.id) || [];
    return payments.reduce((sum, payment) => sum + payment.montantPaye, 0);
  }

  getRemainingAmount(facture: Facture): number {
    const amountPaid = this.getAmountPaid(facture);
    return Math.max(0, facture.montantTTC - amountPaid);
  }

  formatPaymentMode(mode: string): string {
    const modes: {[key: string]: string} = {
      'CARTE': 'Carte bancaire',
      'VIREMENT': 'Virement',
      'ESPECES': 'Espèces',
      'CHEQUE': 'Chèque'
    };
    return modes[mode] || mode;
  }
}
