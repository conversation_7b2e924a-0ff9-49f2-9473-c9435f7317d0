package org.ms.dashbordservice.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DetteClientDTO {
    private Long clientId;
    private String nomClient;
    private String prenomClient;
    private String emailClient;
    private BigDecimal montantTotalDette;
    private int nombreFacturesImpayees;
    private List<FactureStatusDTO> facturesImpayees;
}
