!SESSION 2025-05-24 00:59:42.729 -----------------------------------------------
eclipse.buildId=4.34.0.20241128-0756
java.version=23.0.2
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=fr_FR
Framework arguments:  -product org.eclipse.epp.package.jee.product
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.eclipse.epp.package.jee.product -data C:\Users\<USER>\DSIR12\VentePlus\vente_plus_back

!ENTRY ch.qos.logback.classic 1 0 2025-05-24 00:59:44.955
!MESSAGE Activated before the state location was initialized. Retry after the state location is initialized.

!ENTRY ch.qos.logback.classic 1 0 2025-05-24 00:59:46.251
!MESSAGE Logback config file: C:\Users\<USER>\DSIR12\VentePlus\vente_plus_back\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.0.20241001-1350.xml

!ENTRY org.eclipse.ui 2 0 2025-05-24 00:59:46.804
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-05-24 00:59:46.805
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.ui 2 0 2025-05-24 00:59:47.134
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-05-24 00:59:47.134
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.egit.ui 2 0 2025-05-24 00:59:53.596
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>\Users\barka\.p2\pool\plugins\org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_23.0.2.v20250131-0604\jre\bin\javaw.exe
-Dsts.lsp.client=eclipse
-Xmx1024m
-XX:TieredStopAtLevel=1
-Dspring.config.location=classpath:/application.properties
-Xlog:jni+resolve=off
-XX:ErrorFile=C:/Users/<USER>/DSIR12/VentePlus/vente_plus_back/.metadata/.plugins/org.springframework.tooling.boot.ls/fatal-error-spring-boot_1748045218137
-Dlanguageserver.hover-timeout=225
-jar
C:\Users\<USER>\DSIR12\Dev\MicroServices\EDI\eclipse\jee-2023-06\eclipse\..\..\..\..\..\..\..\.p2\pool\plugins\org.springframework.tooling.boot.ls_1.60.0.202502031607\servers\spring-boot-language-server\spring-boot-language-server-1.60.0-SNAPSHOT-exec.jar
END

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:06:58.138
!MESSAGE DelegatingStreamConnectionProvider - Starting Boot LS

!ENTRY org.springframework.tooling.ls.eclipse.commons 1 0 2025-05-24 01:06:58.160
!MESSAGE Started org.springframework.tooling.boot.ls LS process 7572

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:06:58.400
!MESSAGE Classpath changed for project: devis-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:07:17.578
!MESSAGE Classpath changed for project: categorie-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:07:17.816
!MESSAGE Classpath changed for project: categorie-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:07:17.824
!MESSAGE Boot project ADDED: categorie-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:07:18.727
!MESSAGE Classpath changed for project: categorie-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:07:36.285
!MESSAGE Classpath changed for project: client-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:07:37.537
!MESSAGE Classpath changed for project: client-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:07:37.538
!MESSAGE Boot project ADDED: client-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:07:52.050
!MESSAGE Classpath changed for project: config-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:07:52.051
!MESSAGE Boot project ADDED: config-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:08:21.855
!MESSAGE Classpath changed for project: eureka-discoveryservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:08:22.106
!MESSAGE Classpath changed for project: eureka-discoveryservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:08:22.118
!MESSAGE Boot project ADDED: eureka-discoveryservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:08:22.708
!MESSAGE Classpath changed for project: eureka-discoveryservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:08:38.493
!MESSAGE Classpath changed for project: factureservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:08:38.768
!MESSAGE Classpath changed for project: factureservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:08:38.768
!MESSAGE Boot project ADDED: factureservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:08:39.692
!MESSAGE Classpath changed for project: factureservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:08:54.204
!MESSAGE Classpath changed for project: gatewayservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:08:54.205
!MESSAGE Boot project ADDED: gatewayservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:09:10.069
!MESSAGE Classpath changed for project: produitservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:09:10.264
!MESSAGE Classpath changed for project: produitservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:09:10.272
!MESSAGE Boot project ADDED: produitservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 01:09:11.356
!MESSAGE Classpath changed for project: produitservice
!SESSION 2025-05-24 11:18:28.079 -----------------------------------------------
eclipse.buildId=4.34.0.20241128-0756
java.version=23.0.2
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=fr_FR
Framework arguments:  -product org.eclipse.epp.package.jee.product
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.eclipse.epp.package.jee.product

!ENTRY ch.qos.logback.classic 1 0 2025-05-24 11:18:35.954
!MESSAGE Activated before the state location was initialized. Retry after the state location is initialized.

!ENTRY org.eclipse.core.resources 4 567 2025-05-24 11:18:38.113
!MESSAGE Workspace restored, but some problems occurred.
!SUBENTRY 1 org.eclipse.core.resources 4 567 2025-05-24 11:18:38.113
!MESSAGE Could not read metadata for 'factureservice'.
!STACK 1
org.eclipse.core.internal.resources.ResourceException(/factureservice)[567]: java.lang.Exception: The project description file (.project) for 'factureservice' is missing.  This file contains important information about the project.  The project will not function properly until this file is restored.
	at org.eclipse.core.internal.resources.ResourceException.provideStackTrace(ResourceException.java:42)
	at org.eclipse.core.internal.resources.ResourceException.<init>(ResourceException.java:38)
	at org.eclipse.core.internal.localstore.FileSystemResourceManager.read(FileSystemResourceManager.java:970)
	at org.eclipse.core.internal.resources.SaveManager.restoreMetaInfo(SaveManager.java:954)
	at org.eclipse.core.internal.resources.SaveManager.restoreMetaInfo(SaveManager.java:934)
	at org.eclipse.core.internal.resources.SaveManager.restore(SaveManager.java:790)
	at org.eclipse.core.internal.resources.SaveManager.startup(SaveManager.java:1616)
	at org.eclipse.core.internal.resources.Workspace.startup(Workspace.java:2624)
	at org.eclipse.core.internal.resources.Workspace.open(Workspace.java:2325)
	at org.eclipse.core.resources.ResourcesPlugin$WorkspaceInitCustomizer.addingService(ResourcesPlugin.java:591)
	at org.eclipse.core.resources.ResourcesPlugin$WorkspaceInitCustomizer.addingService(ResourcesPlugin.java:1)
	at org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:947)
	at org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:257)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:184)
	at org.osgi.util.tracker.ServiceTracker.open(ServiceTracker.java:324)
	at org.osgi.util.tracker.ServiceTracker.open(ServiceTracker.java:267)
	at org.eclipse.core.resources.ResourcesPlugin.start(ResourcesPlugin.java:565)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:833)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:825)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:775)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1057)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:387)
	at org.eclipse.osgi.container.Module.doStart(Module.java:639)
	at org.eclipse.osgi.container.Module.start(Module.java:498)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:528)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:122)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:620)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:348)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:414)
	at org.eclipse.osgi.internal.loader.sources.SingleSourcePackage.loadClass(SingleSourcePackage.java:41)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass0(BundleLoader.java:516)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:434)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:174)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:528)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:179)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:208)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:143)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:109)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:439)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:271)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:668)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:605)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1481)
!SUBENTRY 2 org.eclipse.core.resources 4 567 2025-05-24 11:18:38.117
!MESSAGE The project description file (.project) for 'factureservice' is missing.  This file contains important information about the project.  The project will not function properly until this file is restored.
!STACK 0
java.lang.Exception: The project description file (.project) for 'factureservice' is missing.  This file contains important information about the project.  The project will not function properly until this file is restored.
	at org.eclipse.core.internal.resources.ResourceException.provideStackTrace(ResourceException.java:42)
	at org.eclipse.core.internal.resources.ResourceException.<init>(ResourceException.java:38)
	at org.eclipse.core.internal.localstore.FileSystemResourceManager.read(FileSystemResourceManager.java:970)
	at org.eclipse.core.internal.resources.SaveManager.restoreMetaInfo(SaveManager.java:954)
	at org.eclipse.core.internal.resources.SaveManager.restoreMetaInfo(SaveManager.java:934)
	at org.eclipse.core.internal.resources.SaveManager.restore(SaveManager.java:790)
	at org.eclipse.core.internal.resources.SaveManager.startup(SaveManager.java:1616)
	at org.eclipse.core.internal.resources.Workspace.startup(Workspace.java:2624)
	at org.eclipse.core.internal.resources.Workspace.open(Workspace.java:2325)
	at org.eclipse.core.resources.ResourcesPlugin$WorkspaceInitCustomizer.addingService(ResourcesPlugin.java:591)
	at org.eclipse.core.resources.ResourcesPlugin$WorkspaceInitCustomizer.addingService(ResourcesPlugin.java:1)
	at org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:947)
	at org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:257)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:184)
	at org.osgi.util.tracker.ServiceTracker.open(ServiceTracker.java:324)
	at org.osgi.util.tracker.ServiceTracker.open(ServiceTracker.java:267)
	at org.eclipse.core.resources.ResourcesPlugin.start(ResourcesPlugin.java:565)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:833)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:825)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:775)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1057)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:387)
	at org.eclipse.osgi.container.Module.doStart(Module.java:639)
	at org.eclipse.osgi.container.Module.start(Module.java:498)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:528)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:122)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:620)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:348)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:414)
	at org.eclipse.osgi.internal.loader.sources.SingleSourcePackage.loadClass(SingleSourcePackage.java:41)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass0(BundleLoader.java:516)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:434)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:174)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:528)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:179)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:208)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:143)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:109)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:439)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:271)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:668)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:605)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1481)

!ENTRY ch.qos.logback.classic 1 0 2025-05-24 11:18:38.883
!MESSAGE Logback config file: C:\Users\<USER>\DSIR12\VentePlus\vente_plus_back\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.0.20241001-1350.xml

!ENTRY org.eclipse.ui 2 0 2025-05-24 11:18:40.158
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-05-24 11:18:40.158
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.ui 2 0 2025-05-24 11:18:40.885
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-05-24 11:18:40.885
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.egit.ui 2 0 2025-05-24 11:18:47.921
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>\Users\barka\.p2\pool\plugins\org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_23.0.2.v20250131-0604\jre\bin\javaw.exe
-Dsts.lsp.client=eclipse
-Xmx1024m
-XX:TieredStopAtLevel=1
-Dspring.config.location=classpath:/application.properties
-Xlog:jni+resolve=off
-XX:ErrorFile=C:/Users/<USER>/DSIR12/VentePlus/vente_plus_back/.metadata/.plugins/org.springframework.tooling.boot.ls/fatal-error-spring-boot_1748081928741
-Dlanguageserver.hover-timeout=225
-jar
C:\Users\<USER>\DSIR12\Dev\MicroServices\EDI\eclipse\jee-2023-06\eclipse\..\..\..\..\..\..\..\.p2\pool\plugins\org.springframework.tooling.boot.ls_1.60.0.202502031607\servers\spring-boot-language-server\spring-boot-language-server-1.60.0-SNAPSHOT-exec.jar
END

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 11:18:48.744
!MESSAGE DelegatingStreamConnectionProvider - Starting Boot LS

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 11:18:48.755
!MESSAGE Boot project ADDED: gatewayservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-24 11:18:48.768
!MESSAGE Boot project ADDED: produitservice

!ENTRY org.springframework.tooling.ls.eclipse.commons 1 0 2025-05-24 11:18:48.785
!MESSAGE Started org.springframework.tooling.boot.ls LS process 12064
!SESSION 2025-05-27 00:26:58.083 -----------------------------------------------
eclipse.buildId=4.34.0.20241128-0756
java.version=23.0.2
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=fr_FR
Framework arguments:  -product org.eclipse.epp.package.jee.product
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.eclipse.epp.package.jee.product

!ENTRY ch.qos.logback.classic 1 0 2025-05-27 00:28:05.657
!MESSAGE Activated before the state location was initialized. Retry after the state location is initialized.

!ENTRY ch.qos.logback.classic 1 0 2025-05-27 00:28:08.455
!MESSAGE Logback config file: C:\Users\<USER>\DSIR12\VentePlus\vente_plus_back\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.0.20241001-1350.xml

!ENTRY org.eclipse.ui 2 0 2025-05-27 00:28:09.679
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-05-27 00:28:09.679
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.ui 2 0 2025-05-27 00:28:10.448
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-05-27 00:28:10.448
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.egit.ui 2 0 2025-05-27 00:28:16.163
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>\Users\barka\.p2\pool\plugins\org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_23.0.2.v20250131-0604\jre\bin\javaw.exe
-Dsts.lsp.client=eclipse
-Xmx1024m
-XX:TieredStopAtLevel=1
-Dspring.config.location=classpath:/application.properties
-Xlog:jni+resolve=off
-XX:ErrorFile=C:/Users/<USER>/DSIR12/VentePlus/vente_plus_back/.metadata/.plugins/org.springframework.tooling.boot.ls/fatal-error-spring-boot_1748302096940
-Dlanguageserver.hover-timeout=225
-jar
C:\Users\<USER>\DSIR12\Dev\MicroServices\EDI\eclipse\jee-2023-06\eclipse\..\..\..\..\..\..\..\.p2\pool\plugins\org.springframework.tooling.boot.ls_1.60.0.202502031607\servers\spring-boot-language-server\spring-boot-language-server-1.60.0-SNAPSHOT-exec.jar
END

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-27 00:28:16.940
!MESSAGE DelegatingStreamConnectionProvider - Starting Boot LS

!ENTRY org.springframework.tooling.ls.eclipse.commons 1 0 2025-05-27 00:28:16.960
!MESSAGE Started org.springframework.tooling.boot.ls LS process 17676

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-27 00:28:55.115
!MESSAGE Classpath changed for project: categorie-service
!SESSION 2025-05-27 13:33:52.793 -----------------------------------------------
eclipse.buildId=4.34.0.20241128-0756
java.version=23.0.2
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=fr_FR
Framework arguments:  -product org.eclipse.epp.package.jee.product
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.eclipse.epp.package.jee.product

!ENTRY ch.qos.logback.classic 1 0 2025-05-27 13:33:56.991
!MESSAGE Activated before the state location was initialized. Retry after the state location is initialized.

!ENTRY ch.qos.logback.classic 1 0 2025-05-27 13:33:58.778
!MESSAGE Logback config file: C:\Users\<USER>\DSIR12\VentePlus\vente_plus_back\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.0.20241001-1350.xml

!ENTRY org.eclipse.ui 2 0 2025-05-27 13:33:59.308
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-05-27 13:33:59.308
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.ui 2 0 2025-05-27 13:33:59.725
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-05-27 13:33:59.725
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.springframework.tooling.ls.eclipse.commons 1 0 2025-05-27 13:34:05.964
!MESSAGE Command list starting LS: org.springframework.tooling.boot.ls
START:
C:\Users\<USER>\.p2\pool\plugins\org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_23.0.2.v20250131-0604\jre\bin\javaw.exe
-Dsts.lsp.client=eclipse
-Xmx1024m
-XX:TieredStopAtLevel=1
-Dspring.config.location=classpath:/application.properties
-Xlog:jni+resolve=off
-XX:ErrorFile=C:/Users/<USER>/DSIR12/VentePlus/vente_plus_back/.metadata/.plugins/org.springframework.tooling.boot.ls/fatal-error-spring-boot_1748349245964
-Dlanguageserver.hover-timeout=225
-jar
C:\Users\<USER>\DSIR12\Dev\MicroServices\EDI\eclipse\jee-2023-06\eclipse\..\..\..\..\..\..\..\.p2\pool\plugins\org.springframework.tooling.boot.ls_1.60.0.202502031607\servers\spring-boot-language-server\spring-boot-language-server-1.60.0-SNAPSHOT-exec.jar
END

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-27 13:34:05.964
!MESSAGE DelegatingStreamConnectionProvider - Starting Boot LS

!ENTRY org.springframework.tooling.ls.eclipse.commons 1 0 2025-05-27 13:34:05.995
!MESSAGE Started org.springframework.tooling.boot.ls LS process 26804

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-27 13:34:10.377
!MESSAGE Boot project ADDED: categorie-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-27 13:34:10.377
!MESSAGE Starting Boot LS...

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-27 13:34:10.384
!MESSAGE Started Boot LS process

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-27 13:34:10.392
!MESSAGE Boot project ADDED: client-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-27 13:34:10.400
!MESSAGE Boot project ADDED: config-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-27 13:34:10.400
!MESSAGE Boot project ADDED: devis-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-27 13:34:10.409
!MESSAGE Boot project ADDED: eureka-discoveryservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-27 13:34:10.421
!MESSAGE Boot project ADDED: gatewayservice

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-27 13:34:10.423
!MESSAGE Boot project ADDED: produitservice

!ENTRY org.eclipse.egit.ui 2 0 2025-05-27 13:34:10.499
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>\Users\barka\DSIR12\VentePlus\vente_plus_back\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.0.20241001-1350.xml

!ENTRY org.eclipse.ui 2 0 2025-05-27 23:54:09.931
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-05-27 23:54:09.931
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.ui 2 0 2025-05-27 23:54:10.327
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-05-27 23:54:10.327
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-05-27 23:54:15.908
!MESSAGE The given URI https://jakarta.ee/xml/ns/jakartaee/application-client_9.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-05-27 23:54:15.909
!MESSAGE The given URI https://jakarta.ee/xml/ns/jakartaee/jobXML_2_0.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-05-27 23:54:15.910
!MESSAGE The given URI https://jakarta.ee/xml/ns/jakartaee/beans_3_0.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-05-27 23:54:15.911
!MESSAGE The given URI https://jakarta.ee/xml/ns/jaxb/bindingschema_3_0.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-05-27 23:54:15.918
!MESSAGE The given URI https://jakarta.ee/xml/ns/validation/validation-configuration-3.0.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-05-27 23:54:15.921
!MESSAGE The given URI http://xmlns.jcp.org/xml/ns/javaee/web-facesconfig_2_1.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.springframework.tooling.ls.eclipse.commons 1 0 2025-05-27 23:54:15.921
!MESSAGE Command list starting LS: org.springframework.tooling.boot.ls
START:
C:\Users\<USER>\.p2\pool\plugins\org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_23.0.2.v20250131-0604\jre\bin\javaw.exe
-Dsts.lsp.client=eclipse
-Xmx1024m
-XX:TieredStopAtLevel=1
-Dspring.config.location=classpath:/application.properties
-Xlog:jni+resolve=off
-XX:ErrorFile=C:/Users/<USER>/DSIR12/VentePlus/vente_plus_back/.metadata/.plugins/org.springframework.tooling.boot.ls/fatal-error-spring-boot_1748386455920
-Dlanguageserver.hover-timeout=225
-jar
C:\Users\<USER>\DSIR12\Dev\MicroServices\EDI\eclipse\jee-2023-06\eclipse\..\..\..\..\..\..\..\.p2\pool\plugins\org.springframework.tooling.boot.ls_1.60.0.202502031607\servers\spring-boot-language-server\spring-boot-language-server-1.60.0-SNAPSHOT-exec.jar
END

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-27 23:54:15.923
!MESSAGE DelegatingStreamConnectionProvider - Starting Boot LS

!ENTRY org.springframework.tooling.ls.eclipse.commons 1 0 2025-05-27 23:54:15.944
!MESSAGE Started org.springframework.tooling.boot.ls LS process 14744

!ENTRY org.eclipse.egit.ui 2 0 2025-05-27 23:54:18.475
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>\Users\barka\DSIR12\VentePlus\vente_plus_back\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.0.20241001-1350.xml

!ENTRY org.eclipse.ui 2 0 2025-05-29 02:16:17.982
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-05-29 02:16:17.982
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.ui 2 0 2025-05-29 02:16:18.383
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-05-29 02:16:18.383
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-05-29 02:16:24.649
!MESSAGE The given URI https://jakarta.ee/xml/ns/jakartaee/application-client_9.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-05-29 02:16:24.651
!MESSAGE The given URI https://jakarta.ee/xml/ns/jakartaee/jobXML_2_0.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-05-29 02:16:24.653
!MESSAGE The given URI https://jakarta.ee/xml/ns/jakartaee/beans_3_0.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-05-29 02:16:24.655
!MESSAGE The given URI https://jakarta.ee/xml/ns/jaxb/bindingschema_3_0.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-05-29 02:16:24.668
!MESSAGE The given URI https://jakarta.ee/xml/ns/validation/validation-configuration-3.0.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.springframework.tooling.ls.eclipse.commons 1 0 2025-05-29 02:16:24.675
!MESSAGE Command list starting LS: org.springframework.tooling.boot.ls
START:
C:\Users\<USER>\.p2\pool\plugins\org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_23.0.2.v20250131-0604\jre\bin\javaw.exe
-Dsts.lsp.client=eclipse
-Xmx1024m
-XX:TieredStopAtLevel=1
-Dspring.config.location=classpath:/application.properties
-Xlog:jni+resolve=off
-XX:ErrorFile=C:/Users/<USER>/DSIR12/VentePlus/vente_plus_back/.metadata/.plugins/org.springframework.tooling.boot.ls/fatal-error-spring-boot_1748481384674
-Dlanguageserver.hover-timeout=225
-jar
C:\Users\<USER>\DSIR12\Dev\MicroServices\EDI\eclipse\jee-2023-06\eclipse\..\..\..\..\..\..\..\.p2\pool\plugins\org.springframework.tooling.boot.ls_1.60.0.202502031607\servers\spring-boot-language-server\spring-boot-language-server-1.60.0-SNAPSHOT-exec.jar
END

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-05-29 02:16:24.675
!MESSAGE The given URI http://xmlns.jcp.org/xml/ns/javaee/web-facesconfig_2_1.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-29 02:16:24.676
!MESSAGE DelegatingStreamConnectionProvider - Starting Boot LS

!ENTRY org.springframework.tooling.ls.eclipse.commons 1 0 2025-05-29 02:16:24.705
!MESSAGE Started org.springframework.tooling.boot.ls LS process 39856

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-29 02:16:27.202
!MESSAGE Boot project ADDED: categorie-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-29 02:16:27.205
!MESSAGE Starting Boot LS...

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-29 02:16:27.211
!MESSAGE Started Boot LS process

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-29 02:16:27.232
!MESSAGE Boot project ADDED: client-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-29 02:16:27.261
!MESSAGE Boot project ADDED: config-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-29 02:16:27.278
!MESSAGE Boot project ADDED: devis-service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-05-29 02:16:27.298
!MESSAGE Boot project ADDED: eureka-discoveryservice

!ENTRY org.eclipse.egit.ui 2 0 2025-05-29 02:16:27.325
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>\Users\barka\DSIR12\VentePlus\vente_plus_back

!ENTRY ch.qos.logback.classic 1 0 2025-06-01 13:23:12.669
!MESSAGE Activated before the state location was initialized. Retry after the state location is initialized.

!ENTRY ch.qos.logback.classic 1 0 2025-06-01 13:23:14.055
!MESSAGE Logback config file: C:\Users\<USER>\DSIR12\VentePlus\vente_plus_back\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.0.20241001-1350.xml

!ENTRY org.eclipse.ui 2 0 2025-06-01 13:23:14.606
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-06-01 13:23:14.606
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.ui 2 0 2025-06-01 13:23:14.916
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-06-01 13:23:14.916
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-06-01 13:23:19.231
!MESSAGE The given URI https://jakarta.ee/xml/ns/jakartaee/application-client_9.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-06-01 13:23:19.232
!MESSAGE The given URI https://jakarta.ee/xml/ns/jakartaee/jobXML_2_0.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-06-01 13:23:19.233
!MESSAGE The given URI https://jakarta.ee/xml/ns/jakartaee/beans_3_0.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-06-01 13:23:19.234
!MESSAGE The given URI https://jakarta.ee/xml/ns/jaxb/bindingschema_3_0.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-06-01 13:23:19.242
!MESSAGE The given URI https://jakarta.ee/xml/ns/validation/validation-configuration-3.0.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.springframework.tooling.ls.eclipse.commons 1 0 2025-06-01 13:23:19.245
!MESSAGE Command list starting LS: org.springframework.tooling.boot.ls
START:
C:\Users\<USER>\.p2\pool\plugins\org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_23.0.2.v20250131-0604\jre\bin\javaw.exe
-Dsts.lsp.client=eclipse
-Xmx1024m
-XX:TieredStopAtLevel=1
-Dspring.config.location=classpath:/application.properties
-Xlog:jni+resolve=off
-XX:ErrorFile=C:/Users/<USER>/DSIR12/VentePlus/vente_plus_back/.metadata/.plugins/org.springframework.tooling.boot.ls/fatal-error-spring-boot_1748780599245
-Dlanguageserver.hover-timeout=225
-jar
C:\Users\<USER>\DSIR12\Dev\MicroServices\EDI\eclipse\jee-2023-06\eclipse\..\..\..\..\..\..\..\.p2\pool\plugins\org.springframework.tooling.boot.ls_1.60.0.202502031607\servers\spring-boot-language-server\spring-boot-language-server-1.60.0-SNAPSHOT-exec.jar
END

!ENTRY org.eclipse.wildwebdeveloper.xml 2 0 2025-06-01 13:23:19.246
!MESSAGE The given URI http://xmlns.jcp.org/xml/ns/javaee/web-facesconfig_2_1.xsd from org.eclipse.jst.standard.schemas could not be resolved for local access

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-06-01 13:23:19.246
!MESSAGE DelegatingStreamConnectionProvider - Starting Boot LS

!ENTRY org.springframework.tooling.ls.eclipse.commons 1 0 2025-06-01 13:23:19.266
!MESSAGE Started org.springframework.tooling.boot.ls LS process 35192

!ENTRY org.eclipse.egit.ui 2 0 2025-06-01 13:23:21.257
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>