# Simple Test for Dashboard Service
Write-Host "=== Simple Dashboard Service Test ===" -ForegroundColor Green

Write-Host "`nTesting Dashboard Service Health..." -ForegroundColor Blue
try {
    $health = Invoke-RestMethod -Uri "http://localhost:8089/actuator/health"
    Write-Host "✅ Dashboard Service is UP" -ForegroundColor Green
    Write-Host "Status: $($health.status)" -ForegroundColor White
} catch {
    Write-Host "❌ Dashboard Service is DOWN" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nTesting Dashboard Endpoints..." -ForegroundColor Blue

# Test endpoints that should return empty arrays or errors gracefully
$endpoints = @(
    @{Url="http://localhost:8089/dashboard/clients/analytics"; Description="All Clients Analytics"},
    @{Url="http://localhost:8089/dashboard/clients/fideles?limit=5"; Description="Most Loyal Clients"},
    @{Url="http://localhost:8089/dashboard/produits/plus-vendus?limit=10"; Description="Best Selling Products"},
    @{Url="http://localhost:8089/dashboard/produits/rupture-stock"; Description="Out of Stock Products"},
    @{Url="http://localhost:8089/dashboard/factures/reglees"; Description="Paid Invoices"},
    @{Url="http://localhost:8089/dashboard/factures/non-reglees"; Description="Unpaid Invoices"},
    @{Url="http://localhost:8089/dashboard/dettes"; Description="Client Debts"}
)

foreach ($endpoint in $endpoints) {
    Write-Host "`n--- Testing: $($endpoint.Description) ---" -ForegroundColor Yellow
    Write-Host "GET $($endpoint.Url)" -ForegroundColor Cyan
    
    try {
        $response = Invoke-RestMethod -Uri $endpoint.Url
        Write-Host "✅ Success:" -ForegroundColor Green
        $response | ConvertTo-Json -Depth 2 | Write-Host
    } catch {
        Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        }
    }
}

Write-Host "`nSummary:" -ForegroundColor Cyan
Write-Host "- Dashboard service is running and responding" -ForegroundColor White
Write-Host "- Endpoints are accessible (may show errors if other services are down)" -ForegroundColor White
Write-Host "- To test fully, start all microservices and run the complete test" -ForegroundColor White

Write-Host "`nNext Steps:" -ForegroundColor Magenta
Write-Host "1. Start all microservices (client, produit, facture, reglement, etc.)" -ForegroundColor White
Write-Host "2. Run the complete test script: .\test-complete.ps1" -ForegroundColor White
Write-Host "3. Or follow the manual testing guide in TESTING_GUIDE.md" -ForegroundColor White
