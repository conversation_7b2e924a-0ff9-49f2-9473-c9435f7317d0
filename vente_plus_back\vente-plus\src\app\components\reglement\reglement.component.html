<!-- Reglement Management Header -->
<div class="reglement-header">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">
      <i class="fas fa-credit-card me-2"></i>
      Gestion des Règlements
    </h1>
    <button class="btn btn-primary" (click)="openCreateModal()">
      <i class="fas fa-plus me-2"></i>
      Nouveau Règlement
    </button>
  </div>
</div>

<!-- Loading State -->
<div *ngIf="loading" class="text-center py-5">
  <div class="spinner-border text-primary" role="status">
    <span class="visually-hidden">Chargement...</span>
  </div>
  <p class="mt-3">Chargement des règlements...</p>
</div>

<!-- Error State -->
<div *ngIf="error" class="alert alert-danger" role="alert">
  <i class="fas fa-exclamation-triangle me-2"></i>
  {{ error }}
</div>

<!-- Reglement Content -->
<div *ngIf="!loading && !error">

  <!-- Stats Cards Row -->
  <div class="row mb-4">
    <!-- Total Payments Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-success shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                Total Règlements
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ formatCurrency(totalPayments) }}
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-euro-sign fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Paid Invoices Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-primary shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                Factures Payées
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ paidInvoices.length }}
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-check-circle fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Unpaid Invoices Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-danger shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                Factures Impayées
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ unpaidInvoices.length }}
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Partial Payments Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-warning shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                Paiements Partiels
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ partialInvoices.length }}
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-clock fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters Row -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-primary">Filtres</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3">
              <label for="filterClient" class="form-label">Client</label>
              <select id="filterClient" class="form-select" [(ngModel)]="selectedClientId" (change)="applyFilters()">
                <option value="">Tous les clients</option>
                <option *ngFor="let client of clients" [value]="client.id">
                  {{ client.prenom }} {{ client.nom }}
                </option>
              </select>
            </div>
            <div class="col-md-3">
              <label for="filterMode" class="form-label">Mode de Paiement</label>
              <select id="filterMode" class="form-select" [(ngModel)]="selectedPaymentMode" (change)="applyFilters()">
                <option value="">Tous les modes</option>
                <option *ngFor="let mode of paymentModes" [value]="mode">
                  {{ formatPaymentMode(mode) }}
                </option>
              </select>
            </div>
            <div class="col-md-3">
              <label for="filterYear" class="form-label">Année</label>
              <select id="filterYear" class="form-select" [(ngModel)]="selectedYear" (change)="applyFilters()">
                <option value="">Toutes les années</option>
                <option *ngFor="let year of years" [value]="year">{{ year }}</option>
              </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
              <button class="btn btn-secondary me-2" (click)="clearFilters()">
                <i class="fas fa-times me-1"></i>
                Effacer
              </button>
              <button class="btn btn-info" (click)="refreshData()">
                <i class="fas fa-sync-alt me-1"></i>
                Actualiser
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Reglements Table -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-primary">Liste des Règlements</h6>
          <span class="badge bg-primary">{{ filteredReglements.length }} règlement(s)</span>
        </div>
        <div class="card-body">
          <div *ngIf="filteredReglements.length === 0" class="text-center text-muted py-5">
            <i class="fas fa-credit-card fa-3x mb-3"></i>
            <p>Aucun règlement trouvé</p>
          </div>
          <div *ngIf="filteredReglements.length > 0" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Numéro</th>
                  <th>Facture</th>
                  <th>Client</th>
                  <th>Montant</th>
                  <th>Mode</th>
                  <th>Date</th>
                  <th>Référence</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let reglement of filteredReglements">
                  <td>
                    <span class="badge bg-info">{{ reglement.numeroReglement }}</span>
                  </td>
                  <td>
                    <span class="badge bg-secondary">Facture #{{ reglement.factureId }}</span>
                  </td>
                  <td>
                    <span *ngIf="getClientById(reglement.clientId) as client">
                      {{ client.prenom }} {{ client.nom }}
                    </span>
                  </td>
                  <td class="text-success font-weight-bold">
                    {{ formatCurrency(reglement.montantPaye) }}
                  </td>
                  <td>
                    <span class="badge" [ngClass]="getPaymentModeClass(reglement.modeReglement)">
                      {{ formatPaymentMode(reglement.modeReglement) }}
                    </span>
                  </td>
                  <td>{{ reglement.dateReglement | date:'short' }}</td>
                  <td>{{ reglement.reference }}</td>
                  <td>
                    <div class="btn-group" role="group">
                      <button class="btn btn-sm btn-outline-primary"
                              (click)="viewReglement(reglement)"
                              title="Voir détails">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-warning"
                              (click)="editReglement(reglement)"
                              title="Modifier">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger"
                              (click)="deleteReglement(reglement)"
                              title="Supprimer">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>

<!-- Create/Edit Reglement Modal -->
<div class="modal fade" id="reglementModal" tabindex="-1" aria-labelledby="reglementModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="reglementModalLabel">
          {{ isEditMode ? 'Modifier' : 'Nouveau' }} Règlement
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form [formGroup]="reglementForm" (ngSubmit)="saveReglement()">
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <label for="factureId" class="form-label">Facture *</label>
              <select id="factureId" class="form-select" formControlName="factureId" required>
                <option value="">Sélectionner une facture</option>
                <option *ngFor="let invoice of availableInvoices" [value]="invoice.facture.id">
                  Facture #{{ invoice.facture.id }} - {{ invoice.client.prenom }} {{ invoice.client.nom }}
                  ({{ formatCurrency(invoice.montantRestant) }} restant)
                </option>
              </select>
              <div *ngIf="reglementForm.get('factureId')?.invalid && reglementForm.get('factureId')?.touched"
                   class="text-danger small">
                Veuillez sélectionner une facture
              </div>
            </div>
            <div class="col-md-6">
              <label for="montantPaye" class="form-label">Montant à Payer *</label>
              <div class="input-group">
                <input type="number"
                       id="montantPaye"
                       class="form-control"
                       formControlName="montantPaye"
                       step="0.01"
                       min="0.01"
                       placeholder="0.00"
                       required>
                <span class="input-group-text">€</span>
              </div>
              <div *ngIf="reglementForm.get('montantPaye')?.invalid && reglementForm.get('montantPaye')?.touched"
                   class="text-danger small">
                Veuillez saisir un montant valide
              </div>
            </div>
          </div>

          <div class="row mt-3">
            <div class="col-md-6">
              <label for="modeReglement" class="form-label">Mode de Paiement *</label>
              <select id="modeReglement" class="form-select" formControlName="modeReglement" required>
                <option value="">Sélectionner un mode</option>
                <option *ngFor="let mode of paymentModes" [value]="mode">
                  {{ formatPaymentMode(mode) }}
                </option>
              </select>
              <div *ngIf="reglementForm.get('modeReglement')?.invalid && reglementForm.get('modeReglement')?.touched"
                   class="text-danger small">
                Veuillez sélectionner un mode de paiement
              </div>
            </div>
            <div class="col-md-6">
              <label for="reference" class="form-label">Référence *</label>
              <input type="text"
                     id="reference"
                     class="form-control"
                     formControlName="reference"
                     placeholder="Référence du paiement"
                     required>
              <div *ngIf="reglementForm.get('reference')?.invalid && reglementForm.get('reference')?.touched"
                   class="text-danger small">
                Veuillez saisir une référence
              </div>
            </div>
          </div>

          <div class="row mt-3">
            <div class="col-12">
              <label for="commentaire" class="form-label">Commentaire</label>
              <textarea id="commentaire"
                        class="form-control"
                        formControlName="commentaire"
                        rows="3"
                        placeholder="Commentaire optionnel..."></textarea>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
          <button type="submit"
                  class="btn btn-primary"
                  [disabled]="reglementForm.invalid || saving">
            <i *ngIf="saving" class="fas fa-spinner fa-spin me-2"></i>
            {{ isEditMode ? 'Modifier' : 'Créer' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
