:host {
  --bg-primary: #f4f6f9;
  --bg-secondary: #f9fafb;
  --text-primary: #23272b;
  --text-secondary: #5a6270;
  --border-color: #e3e6ea;
  --shadow-color: rgba(136, 152, 170, 0.15);
  --hover-bg: #f1f3f7;
  --input-border: #e3e6ea;
  --input-focus: rgba(94, 114, 228, 0.08);
}

:host-context(.dark-theme) {
  --bg-primary: #181a1b;
  --bg-secondary: #23272b;
  --text-primary: #f1f1f1;
  --text-secondary: #b0b8c1;
  --border-color: #353a40;
  --shadow-color: rgba(0,0,0,0.7);
  --hover-bg: #23272b;
  --input-border: #353a40;
  --input-focus: rgba(94, 114, 228, 0.2);
}

.category-container {
  padding: 24px;
  background-color: var(--bg-primary, #f4f6f9);
  min-height: 100vh;
  transition: background-color 0.3s ease;
}

.filter-toggle-btn {
  margin-left: 12px;
  background: #5e72e4;         /* <PERSON><PERSON> de #11cdef à #5e72e4 */
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 18px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 7px;
  height: 40px;
  box-shadow: 0 2px 8px 0 var(--shadow-color);
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
}

.filter-toggle-btn:hover {
  background: #4a5cd1;         /* Changé de #0fb8d4 à #4a5cd1 */
  transform: translateY(-1px);
  box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.filter-toggle-btn i {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background-color: var(--bg-secondary, #f9fafb);
  padding: 20px;
  border-radius: 14px;
  box-shadow: 0 4px 24px 0 var(--shadow-color);
  transition: all 0.3s ease;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.header h2 {
  color: var(--text-primary, #23272b);
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  transition: color 0.3s ease;
}

.subtitle {
  color: var(--text-secondary, #5a6270);
  margin: 0;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.add-button {
  background-color: #5e72e4;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-button:hover {
  background-color: #4a5cd1;
  transform: translateY(-1px);
  box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.add-button i {
  font-size: 1.2rem;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.stat-card {
  background-color: var(--bg-secondary, #f9fafb);
  padding: 20px;
  border-radius: 14px;
  box-shadow: 0 4px 24px 0 var(--shadow-color);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: #5e72e4;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.stat-info h3 {
  margin: 0;
  color: var(--text-primary, #23272b);
  font-size: 1.5rem;
  font-weight: 600;
  transition: color 0.3s ease;
}

.stat-info p {
  margin: 0;
  color: var(--text-secondary, #5a6270);
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.table-container {
  background-color: var(--bg-secondary, #f9fafb);
  border-radius: 14px;
  box-shadow: 0 4px 24px 0 var(--shadow-color);
  overflow: hidden;
  transition: all 0.3s ease;
}

.table-header {
  padding: 20px;
  border-bottom: 1.5px solid var(--border-color, #e3e6ea);
  transition: border-color 0.3s ease;
}

.search-filter-row {
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: flex-start;
  margin-bottom: 0;
}

.search-box {
  flex: 1 1 0%;
  display: flex;
  align-items: center;
  position: relative;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary, #5a6270);
  transition: color 0.3s ease;
}

.search-box input {
  width: 100%;
  padding: 10px 10px 10px 40px;
  border: 1px solid var(--input-border, #e3e6ea);
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  background-color: var(--bg-secondary, #f9fafb);
  color: var(--text-primary, #23272b);
}

.search-box input::placeholder {
  color: var(--text-secondary, #5a6270);
}

.search-box input:focus {
  outline: none;
  border-color: #5e72e4;
  box-shadow: 0 0 0 3px var(--input-focus, rgba(94, 114, 228, 0.08));
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

th, td {
  padding: 16px 20px;
  text-align: left;
  border-bottom: 1.5px solid var(--border-color, #e3e6ea);
  transition: all 0.3s ease;
}

th {
  background-color: var(--hover-bg, #f1f3f7);
  color: var(--text-primary, #23272b);
  font-weight: 700;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.04em;
  border-bottom: 2px solid var(--border-color, #e3e6ea);
  transition: all 0.3s ease;
}

tr {
  transition: background-color 0.3s ease;
}

tr:hover {
  background-color: rgba(94, 114, 228, 0.07);
  transition: background 0.2s;
}

.actions {
  display: flex;
  gap: 8px;
}

.edit-btn, .delete-btn, .validate-btn, .cancel-btn, .view-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.edit-btn {
  background-color: #11cdef;
  box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
}

.edit-btn:hover {
  background-color: #0fb8d4;
  transform: translateY(-1px);
  box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.delete-btn {
  background-color: #f5365c;
  box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
}

.delete-btn:hover {
  background-color: #e31a3c;
  transform: translateY(-1px);
  box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.validate-btn {
  background-color: #2dce89;
  box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
}

.validate-btn:hover {
  background-color: #24a46d;
  transform: translateY(-1px);
  box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.cancel-btn {
  background-color: #fb6340;
  box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
}

.cancel-btn:hover {
  background-color: #ea4f2c;
  transform: translateY(-1px);
  box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.view-btn {
  background-color: #11cdef;
  box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
}

.view-btn:hover {
  background-color: #0fb8d4;
  transform: translateY(-1px);
  box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.edit-btn i, .delete-btn i, .validate-btn i, .cancel-btn i, .view-btn i {
  font-size: 0.875rem;
}

td {
  color: var(--text-secondary, #5a6270);
  font-size: 0.95rem;
  vertical-align: middle;
  transition: color 0.3s ease;
}

td:first-child {
  color: var(--text-primary, #23272b);
  font-weight: 600;
  transition: color 0.3s ease;
}

.badge {
  padding: 0.5em 0.75em;
  font-size: 0.875em;
  font-weight: 500;
  border-radius: 0.25rem;
}

.badge-warning {
  background-color: #ffc107;
  color: #000;
}

.badge-success {
  background-color: #2dce89;
  color: #fff;
}

.badge-danger {
  background-color: #f5365c;
  color: #fff;
}

.badge-info {
  background-color: #11cdef;
  color: #fff;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin: 18px 0 0 0;
}

.pagination button {
  background: var(--bg-secondary, #f9fafb);
  color: var(--text-primary, #23272b);
  border: 1.5px solid var(--border-color, #e3e6ea);
  border-radius: 6px;
  padding: 6px 14px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, border 0.2s;
}

.pagination button.active,
.pagination button:hover:not(:disabled) {
  background: #5e72e4;
  color: #fff;
  border-color: #5e72e4;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(44, 62, 80, 0.25);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeInModal 0.2s;
}

@keyframes fadeInModal {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background: var(--bg-secondary, #fff);
  border-radius: 18px;
  box-shadow: 0 8px 32px 0 var(--shadow-color);
  min-width: 340px;
  max-width: 800px;
  width: 100%;
  padding: 0;
  overflow: hidden;
  animation: popInModal 0.25s cubic-bezier(.4,2,.6,1);
  display: flex;
  flex-direction: column;
}

@keyframes popInModal {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 22px 28px 12px 28px;
  background: var(--bg-secondary, #f9fafb);
  border-bottom: 1.5px solid var(--border-color, #e3e6ea);
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary, #23272b);
}

.modal-title i {
  font-size: 1.4rem;
  color: #5e72e4;
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-secondary, #5a6270);
  font-size: 1.3rem;
  cursor: pointer;
  transition: color 0.2s;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #f1f3f7;
  color: #e31a3c;
}

.modal-body {
  padding: 32px 40px 36px 40px;
  background: var(--bg-primary, #f4f6f9);
}

/* Facture details styles */
.facture-details {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.facture-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.client-info, .facture-info {
  background: var(--bg-secondary, #fff);
  padding: 20px;
  border-radius: 14px;
  box-shadow: 0 4px 12px 0 var(--shadow-color);
}

.client-info h3, .facture-info h3 {
  margin-top: 0;
  color: var(--text-primary);
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.client-info p, .facture-info p {
  margin: 8px 0;
  color: var(--text-secondary);
}

.facture-lignes {
  background: var(--bg-secondary, #fff);
  padding: 20px;
  border-radius: 14px;
  box-shadow: 0 4px 12px 0 var(--shadow-color);
}

.facture-totals {
  background: var(--bg-secondary, #fff);
  padding: 20px;
  border-radius: 14px;
  box-shadow: 0 4px 12px 0 var(--shadow-color);
}

.total-row {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.total-row.total {
  font-weight: 700;
  font-size: 1.2rem;
  border-bottom: none;
  color: var(--text-primary);
  padding-top: 16px;
}

.alert {
  position: fixed;
  top: 32px;
  left: 50%;
  transform: translateX(-50%);
  min-width: 260px;
  max-width: 90vw;
  padding: 16px 32px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 1.05rem;
  z-index: 2000;
  box-shadow: 0 4px 24px 0 var(--shadow-color);
  animation: fadeInAlert 0.4s cubic-bezier(.4,2,.6,1);
  cursor: pointer;
  text-align: center;
  transition: background 0.2s, color 0.2s;
}

@keyframes fadeInAlert {
  from { opacity: 0; transform: translateX(-50%) translateY(-20px);}
  to   { opacity: 1; transform: translateX(-50%) translateY(0);}
}

.alert.success {
  background: #5e72e4;
  color: #fff;
}

.alert.error {
  background: #f5365c;
  color: #fff;
}

.alert.delete {
  background: #ff9800;
  color: #fff;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32px;
}

.loading-spinner .spinner {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .facture-header {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .modal-content {
    width: 95%;
    max-width: 95%;
  }
  
  .modal-body {
    padding: 20px;
  }
}

.filters-advanced {
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transition: max-height 0.4s cubic-bezier(.4,2,.6,1), opacity 0.3s;
  margin-top: 0;
}

.filters-advanced.open {
  max-height: 200px;
  opacity: 1;
  margin-top: 18px;
}

.filters-content {
  display: flex;
  gap: 16px;
  background: var(--bg-primary, #f4f6f9);
  border-radius: 10px;
  padding: 18px 18px 10px 18px;
  box-shadow: 0 2px 10px 0 var(--shadow-color);
  align-items: flex-end;
  flex-wrap: wrap;
}

.filters-content .form-control {
  min-width: 160px;
  max-width: 220px;
  padding: 10px 12px;
  border: 1px solid var(--input-border, #e3e6ea);
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  background-color: var(--bg-secondary, #f9fafb);
  color: var(--text-primary, #23272b);
}

.filters-content .form-control:focus {
  outline: none;
  border-color: #5e72e4;
  box-shadow: 0 0 0 3px var(--input-focus, rgba(94, 114, 228, 0.08));
}

.filters-content .form-control::placeholder {
  color: var(--text-secondary, #5a6270);
}

.reset-filters-btn {
  background: #f5365c;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 7px;
  cursor: pointer;
  transition: background 0.2s;
  margin-left: 8px;
}

.reset-filters-btn i {
  font-size: 1.1rem;
}

.reset-filters-btn:hover {
  background: #e31a3c;
}

@media (max-width: 900px) {
  .filters-content {
    flex-direction: column;
    gap: 10px;
    padding: 12px 8px 8px 8px;
  }
  .filters-content .form-control {
    min-width: 0;
    max-width: 100%;
  }
}



@media (max-width: 900px) {
  .search-filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  .filter-toggle-btn {
    width: 100%;
    margin-left: 0;
  }
}

.export-btn {
  background-color: #fb6340;
  box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
}

.export-btn:hover {
  background-color: #ea4f2c;
  transform: translateY(-1px);
  box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

/* ==================== PAYMENT STYLES ==================== */

/* Payment Status */
.payment-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-align: center;
  display: inline-block;
  min-width: 100px;
}

.status-paid {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-unpaid {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-partial {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

/* Payment Buttons */
.payment-btn {
  color: #28a745;
  background-color: rgba(40, 167, 69, 0.1);
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-btn:hover {
  background-color: rgba(40, 167, 69, 0.2);
  transform: translateY(-2px);
}

.history-btn {
  color: #6c757d;
  background-color: rgba(108, 117, 125, 0.1);
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-btn:hover {
  background-color: rgba(108, 117, 125, 0.2);
  transform: translateY(-2px);
}

/* Payment Form */
.payment-form {
  max-width: 600px;
}

.invoice-summary {
  background: var(--bg-secondary, #f9fafb);
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color, #e3e6ea);
}

.invoice-summary h4 {
  margin: 0 0 15px 0;
  color: var(--text-primary, #23272b);
  font-size: 1.2rem;
}

.invoice-summary p {
  margin: 8px 0;
  color: var(--text-secondary, #5a6270);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--text-primary, #23272b);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--input-border, #e3e6ea);
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background-color: var(--bg-secondary, #fff);
  color: var(--text-primary, #23272b);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #5e72e4;
  box-shadow: 0 0 0 3px rgba(94, 114, 228, 0.1);
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 30px;
}

.cancel-btn {
  background: #6c757d;
  color: #fff;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.submit-btn {
  background: #28a745;
  color: #fff;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.submit-btn:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-1px);
}

.submit-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* Payment History */
.payment-history {
  max-width: 800px;
}

.payment-history h4 {
  margin: 0 0 20px 0;
  color: var(--text-primary, #23272b);
}

.no-payments {
  text-align: center;
  padding: 40px;
  color: var(--text-secondary, #5a6270);
}

.payments-list table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.payments-list th,
.payments-list td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--border-color, #e3e6ea);
}

.payments-list th {
  background: var(--bg-secondary, #f9fafb);
  font-weight: 600;
  color: var(--text-primary, #23272b);
}

.payments-list td {
  color: var(--text-secondary, #5a6270);
}

/* Responsive Design for Payment Features */
@media (max-width: 768px) {
  .payment-form,
  .payment-history {
    max-width: 100%;
  }

  .invoice-summary {
    padding: 15px;
  }

  .form-actions {
    flex-direction: column;
  }

  .cancel-btn,
  .submit-btn {
    width: 100%;
  }

  .payments-list {
    overflow-x: auto;
  }

  .payments-list table {
    min-width: 500px;
  }
}
