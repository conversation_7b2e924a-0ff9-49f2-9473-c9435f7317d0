/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['ff-adlm-lr'] = ["ff-Adlm-LR",[["𞤢","𞤩"],["𞤀𞤎","𞤇𞤎"],u],[["𞤀𞤎","𞤇𞤎"],u,u],[["𞤈","𞤀𞥄","𞤃","𞤔","𞤐","𞤃","𞤖"],["𞤈𞤫𞤬","𞤀𞥄𞤩𞤵","𞤃𞤢𞤦","𞤔𞤫𞤧","𞤐𞤢𞥄𞤧","𞤃𞤢𞤣","𞤖𞤮𞤪"],["𞤈𞤫𞤬𞤦𞤭𞤪𞥆𞤫","𞤀𞥄𞤩𞤵𞤲𞥋𞤣𞤫","𞤃𞤢𞤱𞤦𞤢𞥄𞤪𞤫","𞤐𞤶𞤫𞤧𞤤𞤢𞥄𞤪𞤫","𞤐𞤢𞥄𞤧𞤢𞥄𞤲𞤣𞤫","𞤃𞤢𞤱𞤲𞤣𞤫","𞤖𞤮𞤪𞤦𞤭𞤪𞥆𞤫"],["𞤈𞤫𞤬","𞤀𞥄𞤩𞤵","𞤃𞤢𞤦","𞤔𞤫𞤧","𞤐𞤢𞥄𞤧","𞤃𞤢𞤣","𞤖𞤮𞤪"]],u,[["𞤅","𞤕","𞤄","𞤅","𞤁","𞤑","𞤃","𞤔","𞤅","𞤒","𞤔","𞤄"],["𞤅𞤭𞥅𞤤𞤮","𞤕𞤮𞤤𞤼𞤮","𞤐𞤦𞤮𞥅𞤴𞤮","𞤅𞤫𞥅𞤼𞤮","𞤁𞤵𞥅𞤶𞤮","𞤑𞤮𞤪𞤧𞤮","𞤃𞤮𞤪𞤧𞤮","𞤔𞤵𞤳𞤮","𞤅𞤭𞤤𞤼𞤮","𞤒𞤢𞤪𞤳𞤮","𞤔𞤮𞤤𞤮","𞤄𞤮𞤱𞤼𞤮"],u],[["𞤅","𞤕","𞤄","𞤅","𞤁","𞤑","𞤃","𞤔","𞤅","𞤒","𞤔","𞤄"],["𞤅𞤭𞥅𞤤","𞤕𞤮𞤤","𞤐𞤦𞤮𞥅𞤴","𞤅𞤫𞥅𞤼","𞤁𞤵𞥅𞤶","𞤑𞤮𞤪","𞤃𞤮𞤪","𞤔𞤵𞤳","𞤅𞤭𞤤","𞤒𞤢𞤪","𞤔𞤮𞤤","𞤄𞤮𞤱"],["𞤅𞤭𞥅𞤤𞤮","𞤕𞤮𞤤𞤼𞤮","𞤐𞤦𞤮𞥅𞤴𞤮","𞤅𞤫𞥅𞤼𞤮","𞤁𞤵𞥅𞤶𞤮","𞤑𞤮𞤪𞤧𞤮","𞤃𞤮𞤪𞤧𞤮","𞤔𞤵𞤳𞤮","𞤅𞤭𞤤𞤼𞤮","𞤒𞤢𞤪𞤳𞤮","𞤔𞤮𞤤𞤮","𞤄𞤮𞤱𞤼𞤮"]],[["𞤀𞤀𞤋","𞤇𞤀𞤋"],u,["𞤀𞤣𞤮 𞤀𞤲𞥆𞤢𞤦𞤭 𞤋𞥅𞤧𞤢𞥄","𞤇𞤢𞥄𞤱𞤮 𞤀𞤲𞥆𞤢𞤦𞤭 𞤋𞥅𞤧𞤢𞥄"]],1,[6,0],["d-M-y","d MMM⹁ y","d MMMM⹁ y","EEEE d MMMM⹁ y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1} {0}",u,"{1} 𞤉 {0}",u],[".","⹁",";","%","+","-","E","×","‰","∞","𞤏𞤮𞤈",":"],["#,##0.###","#,##0%","¤ #,##0.00","#E0"],"LRD","$","𞤁𞤢𞤤𞤢 𞤂𞤭𞤦𞤫𞤪𞤭𞤴𞤢𞤲𞤳𞤮",{"BYN":[u,"р."],"GNF":[u,"𞤊𞤘"],"JPY":["JP¥","¥"],"LRD":["$"],"NGN":["𞤐𞤐𞤘","₦"],"PGK":["𞤑𞤆𞤘"],"PHP":["𞤆𞤆𞤖","₱"],"USD":["US$","$"],"XAF":["𞤊𞤅𞤊𞤀"],"XOF":["𞤅𞤊𞤀"]},"rtl", plural, []];
  })(globalThis);
    