package org.ms.gatewayservice.utils;


import org.springframework.stereotype.Component;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureException;

@Component
public class JwtUtil {

    private final String secret = "ta_cle_secrete_super_securisee"; // À mettre en config sécurisée

    public boolean validateToken(String token) {
        try {
            Claims claims = Jwts.parser()
                .setSigningKey(secret.getBytes())
                .parseClaimsJws(token)
                .getBody();

            // Tu peux aussi vérifier la date d'expiration ici
            return true;
        } catch (SignatureException | IllegalArgumentException e) {
            return false;
        }
    }
}

