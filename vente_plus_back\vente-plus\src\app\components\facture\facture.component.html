<div class="container mt-4">
  <!-- Alert -->
  <div *ngIf="showAlert" class="alert" [ngClass]="alertType" (click)="closeAlert()">
    {{ alertMessage }}
  </div>

  <div class="category-container">
    <!-- Header -->
    <div class="header">
      <div class="header-left">
        <h2>Gestion des Factures</h2>
        <p class="subtitle">Gérez vos factures et suivez leur statut</p>
      </div>
    </div>

    <!-- Stats -->
    <div class="stats-container">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-receipt"></i>
        </div>
        <div class="stat-info">
          <h3>{{ factures.length }}</h3>
          <p>Total des factures</p>
        </div>
      </div>
    </div>

    <!-- Table -->
    <div class="table-container">
      <div class="table-header">
        <div class="search-filter-row">
          <div class="search-box">
            <i class="fa-solid fa-search"></i>
            <input 
              type="text" 
              [ngModel]="searchTerm" 
              (ngModelChange)="onSearchChange($event)"
              placeholder="Rechercher une facture..."
            >
          </div>
        </div>
      </div>

      <div *ngIf="isLoading" class="loading-spinner">
        <div class="spinner"></div>
      </div>

      <table *ngIf="!isLoading">
        <thead>
          <tr>
            <th>Numéro</th>
            <th>Client</th>
            <th>Date</th>
            <th>Montant HT</th>
            <th>Montant TVA</th>
            <th>Montant TTC</th>
            <th>Statut Paiement</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let f of paginatedFactures">
            <td>{{ f.id }}</td>
            <td>{{ f.client?.nom }} {{ f.client?.prenom }}</td>
            <td>{{ f.dateFacture | date:'dd/MM/yyyy' }}</td>
            <td>{{ f.montantHT | number:'1.2-2' }} €</td>
            <td>{{ f.montantTVA | number:'1.2-2' }} €</td>
            <td>{{ f.montantTTC | number:'1.2-2' }} €</td>
            <td>
              <span class="payment-status" [ngClass]="getPaymentStatusClass(f)">
                {{ getPaymentStatusText(f) }}
              </span>
            </td>
            <td>
              <div class="actions">
                <button class="view-btn" (click)="viewFacture(f)" title="Voir détails">
                  <i class="bi bi-eye-fill"></i>
                </button>
                <button class="payment-btn" (click)="openPaymentModal(f)"
                        *ngIf="!isFactureFullyPaid(f)" title="Effectuer un paiement">
                  <i class="bi bi-credit-card-fill"></i>
                </button>
                <button class="history-btn" (click)="viewPaymentHistory(f)" title="Historique des paiements">
                  <i class="bi bi-clock-history"></i>
                </button>
                <button class="delete-btn" (click)="openDeleteModal(f)" title="Supprimer">
                  <i class="bi bi-trash-fill"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div class="pagination" *ngIf="totalPages > 1">
        <button (click)="prevPage()" [disabled]="currentPage === 1">&laquo;</button>
        <button *ngFor="let page of [].constructor(totalPages); let i = index" 
                (click)="goToPage(i+1)" 
                [class.active]="currentPage === (i+1)">{{ i+1 }}</button>
        <button (click)="nextPage()" [disabled]="currentPage === totalPages">&raquo;</button>
      </div>
    </div>

    <!-- View Modal -->
    <div class="modal" *ngIf="showViewModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="bi bi-receipt"></i>
            <span>Détails de la facture</span>
          </div>
          <button class="close-btn" (click)="closeViewModal()">
            <i class="bi bi-x-lg"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="facture-details" *ngIf="selectedFacture">
            <div class="facture-header">
              <div class="client-info">
                <h3>Client</h3>
                <p>{{ selectedFacture.client?.nom }} {{ selectedFacture.client?.prenom }}</p>
                <p>{{ selectedFacture.client?.email }}</p>
                <p>{{ selectedFacture.client?.telephone }}</p>
              </div>
              <div class="facture-info">
                <h3>Facture #{{ selectedFacture.id }}</h3>
                <p>Date: {{ selectedFacture.dateFacture | date:'dd/MM/yyyy' }}</p>
              </div>
            </div>

            <div class="facture-lignes">
              <table>
                <thead>
                  <tr>
                    <th>Produit</th>
                    <th>Quantité</th>
                    <th>Prix unitaire</th>
                    <th>Total</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let ligne of selectedFacture.facturelignes">
                    <td>{{ ligne.produit?.nom }}</td>
                    <td>{{ ligne.quantite }}</td>
                    <td>{{ ligne.prix | number:'1.2-2' }} DT</td>
                    <td>{{ (ligne.prix * ligne.quantite) | number:'1.2-2' }} DT</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div class="facture-totals">
              <div class="total-row">
                <span>Montant HT:</span>
                <span>{{ selectedFacture.montantHT | number:'1.2-2' }} DT</span>
              </div>
              <div class="total-row">
                <span>TVA (19%):</span>
                <span>{{ selectedFacture.montantTVA | number:'1.2-2' }} DT</span>
              </div>
              <div class="total-row total">
                <span>Montant TTC:</span>
                <span>{{ selectedFacture.montantTTC | number:'1.2-2' }} DT</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment Modal -->
    <div class="modal" *ngIf="showPaymentModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="bi bi-credit-card"></i>
            <span>Effectuer un paiement</span>
          </div>
          <button class="close-btn" (click)="closePaymentModal()">
            <i class="bi bi-x-lg"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="payment-form" *ngIf="selectedFacture">
            <div class="invoice-summary">
              <h4>Facture #{{ selectedFacture.id }}</h4>
              <p><strong>Client:</strong> {{ selectedFacture.client?.nom }} {{ selectedFacture.client?.prenom }}</p>
              <p><strong>Montant total:</strong> {{ selectedFacture.montantTTC | number:'1.2-2' }} €</p>
              <p><strong>Montant payé:</strong> {{ getAmountPaid(selectedFacture) | number:'1.2-2' }} €</p>
              <p><strong>Montant restant:</strong> {{ getRemainingAmount(selectedFacture) | number:'1.2-2' }} €</p>
            </div>

            <form #paymentForm="ngForm" (ngSubmit)="onPaymentSubmit()">
              <div class="form-group">
                <label for="montantPaye">Montant à payer *</label>
                <input type="number"
                       id="montantPaye"
                       name="montantPaye"
                       [(ngModel)]="paymentData.montantPaye"
                       step="0.01"
                       min="0.01"
                       [max]="getRemainingAmount(selectedFacture)"
                       placeholder="0.00"
                       required>
              </div>

              <div class="form-group">
                <label for="modeReglement">Mode de paiement *</label>
                <select id="modeReglement"
                        name="modeReglement"
                        [(ngModel)]="paymentData.modeReglement"
                        required>
                  <option value="">Sélectionner un mode</option>
                  <option value="CARTE">Carte bancaire</option>
                  <option value="VIREMENT">Virement</option>
                  <option value="ESPECES">Espèces</option>
                  <option value="CHEQUE">Chèque</option>
                </select>
              </div>

              <div class="form-group">
                <label for="reference">Référence *</label>
                <input type="text"
                       id="reference"
                       name="reference"
                       [(ngModel)]="paymentData.reference"
                       placeholder="Référence du paiement"
                       required>
              </div>

              <div class="form-group">
                <label for="commentaire">Commentaire</label>
                <textarea id="commentaire"
                          name="commentaire"
                          [(ngModel)]="paymentData.commentaire"
                          rows="3"
                          placeholder="Commentaire optionnel..."></textarea>
              </div>

              <div class="form-actions">
                <button type="button" class="cancel-btn" (click)="closePaymentModal()">
                  Annuler
                </button>
                <button type="submit" class="submit-btn" [disabled]="!paymentForm.valid || isProcessingPayment">
                  <span *ngIf="isProcessingPayment">Traitement...</span>
                  <span *ngIf="!isProcessingPayment">Effectuer le paiement</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment History Modal -->
    <div class="modal" *ngIf="showPaymentHistoryModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="bi bi-clock-history"></i>
            <span>Historique des paiements</span>
          </div>
          <button class="close-btn" (click)="closePaymentHistoryModal()">
            <i class="bi bi-x-lg"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="payment-history" *ngIf="selectedFacture">
            <h4>Facture #{{ selectedFacture.id }}</h4>
            <div *ngIf="paymentHistory.length === 0" class="no-payments">
              <p>Aucun paiement effectué pour cette facture.</p>
            </div>
            <div *ngIf="paymentHistory.length > 0" class="payments-list">
              <table>
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Montant</th>
                    <th>Mode</th>
                    <th>Référence</th>
                    <th>Commentaire</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let payment of paymentHistory">
                    <td>{{ payment.dateReglement | date:'dd/MM/yyyy HH:mm' }}</td>
                    <td>{{ payment.montantPaye | number:'1.2-2' }} €</td>
                    <td>{{ formatPaymentMode(payment.modeReglement) }}</td>
                    <td>{{ payment.reference }}</td>
                    <td>{{ payment.commentaire || '-' }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal" *ngIf="showDeleteModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Confirmer la suppression</h3>
          <button class="close-btn" (click)="closeDeleteModal()">
            <i class="bi bi-x-lg"></i>
          </button>
        </div>
        <div class="modal-body">
          <p>Êtes-vous sûr de vouloir supprimer cette facture ?</p>
          <div class="form-actions">
            <button class="submit-btn" (click)="onDelete()">
              Confirmer
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
