version: '3.8'

services:
  postgres:
    image: postgres:17
    container_name: postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 1023
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  config-service:
    build: ./config-service
    container_name: config-service
    ports:
      - "5555:5555"
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5555/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    environment:
      - SPRING_PROFILES_ACTIVE=default
      - SERVER_PORT=5555
      - SPRING_CLOUD_CONFIG_SERVER_NATIVE_SEARCHLOCATIONS=file:/cloud-conf
    volumes:
      - C:/Users/<USER>/cloud-conf:/cloud-conf:ro

  eureka-service:
    build: ./eureka-discoveryservice
    container_name: eureka-service
    ports:
      - "8761:8761"
    depends_on:
      config-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8761/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  client-service:
    build: ./client-service
    container_name: client-service
    ports:
      - "8084:8084"
    environment:
      - SPRING_DATASOURCE_URL=**************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=1023
      - SPRING_CLOUD_CONFIG_URI=http://config-service:5555
      - EUREKA_CLIENT_SERVICEURL_DEFAULTZONE=http://eureka-service:8761/eureka/
    depends_on:
      postgres:
        condition: service_healthy
      config-service:
        condition: service_healthy
      eureka-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8084/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  categorie-service:
    build: ./categorie-service
    container_name: categorie-service
    ports:
      - "8087:8087"
    environment:
      - SPRING_DATASOURCE_URL=*****************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=1023
      - SPRING_CLOUD_CONFIG_URI=http://config-service:5555
      - EUREKA_CLIENT_SERVICEURL_DEFAULTZONE=http://eureka-service:8761/eureka/
    depends_on:
      client-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8087/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  produit-service:
    build: ./produitservice
    container_name: produit-service
    ports:
      - "8080:8080"
    environment:
      - SPRING_DATASOURCE_URL=***************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=1023
      - SPRING_CLOUD_CONFIG_URI=http://config-service:5555
      - EUREKA_CLIENT_SERVICEURL_DEFAULTZONE=http://eureka-service:8761/eureka/
    depends_on:
      categorie-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  devis-service:
    build: ./devis-service
    container_name: devis-service
    ports:
      - "8085:8085"
    environment:
      - SPRING_DATASOURCE_URL=*************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=1023
      - SPRING_CLOUD_CONFIG_URI=http://config-service:5555
      - EUREKA_CLIENT_SERVICEURL_DEFAULTZONE=http://eureka-service:8761/eureka/
    depends_on:
      produit-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8085/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  facture-service:
    build: ./factureservice
    container_name: facture-service
    ports:
      - "8083:8083"
    environment:
      - SPRING_DATASOURCE_URL=***************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=1023
      - SPRING_CLOUD_CONFIG_URI=http://config-service:5555
      - EUREKA_CLIENT_SERVICEURL_DEFAULTZONE=http://eureka-service:8761/eureka/
    depends_on:
      devis-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8083/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  gateway-service:
    build: ./gatewayservice
    container_name: gateway-service
    ports:
      - "8888:8888"
    environment:
      - SPRING_CLOUD_CONFIG_URI=http://config-service:5555
      - EUREKA_CLIENT_SERVICEURL_DEFAULTZONE=http://eureka-service:8761/eureka/
    depends_on:
      facture-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8888/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

volumes:
  pgdata: