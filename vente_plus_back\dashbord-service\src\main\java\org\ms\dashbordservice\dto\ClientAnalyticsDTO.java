package org.ms.dashbordservice.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClientAnalyticsDTO {
    private Long clientId;
    private String nomClient;
    private String prenomClient;
    private String emailClient;
    
    // Chiffre d'affaires
    private BigDecimal chiffresAffairesTotal;
    private Map<Integer, BigDecimal> chiffresAffairesParAnnee;
    
    // Montants non payés
    private BigDecimal montantNonPaye;
    
    // Factures
    private List<FactureStatusDTO> facturesReglees;
    private List<FactureStatusDTO> facturesNonReglees;
    
    // Produits les plus sollicités
    private List<ProduitPopulariteDTO> produitsLesPlusSollicites;
    
    // Statistiques de fidélité
    private int nombreCommandes;
    private BigDecimal montantMoyenCommande;
}
