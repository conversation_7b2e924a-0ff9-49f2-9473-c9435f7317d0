/* Reglement Component Styles */
.reglement-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 2rem;
  border-radius: 10px;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Card Styles */
.card {
  border: none;
  border-radius: 10px;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
  background: transparent;
  border-bottom: 1px solid #e3e6f0;
  border-radius: 10px 10px 0 0 !important;
}

/* Border Left Colors */
.border-left-primary {
  border-left: 4px solid #4e73df !important;
}

.border-left-success {
  border-left: 4px solid #1cc88a !important;
}

.border-left-info {
  border-left: 4px solid #36b9cc !important;
}

.border-left-warning {
  border-left: 4px solid #f6c23e !important;
}

.border-left-danger {
  border-left: 4px solid #e74a3b !important;
}

/* Stats Cards */
.text-xs {
  font-size: 0.7rem;
}

.font-weight-bold {
  font-weight: 700 !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-primary {
  color: #4e73df !important;
}

.text-success {
  color: #1cc88a !important;
}

.text-info {
  color: #36b9cc !important;
}

.text-warning {
  color: #f6c23e !important;
}

.text-danger {
  color: #e74a3b !important;
}

.text-gray-800 {
  color: #5a5c69 !important;
}

.text-gray-300 {
  color: #dddfeb !important;
}

/* Table Styles */
.table {
  margin-bottom: 0;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #5a5c69;
  font-size: 0.85rem;
  background-color: #f8f9fc;
}

.table td {
  font-size: 0.85rem;
  vertical-align: middle;
  border-top: 1px solid #e3e6f0;
}

.table-hover tbody tr:hover {
  background-color: #f8f9fc;
}

/* Badges */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

/* Button Groups */
.btn-group .btn {
  margin-right: 0;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* Form Styles */
.form-label {
  font-weight: 600;
  color: #5a5c69;
  margin-bottom: 0.5rem;
}

.form-control, .form-select {
  border: 1px solid #d1d3e2;
  border-radius: 0.35rem;
  font-size: 0.85rem;
}

.form-control:focus, .form-select:focus {
  border-color: #4e73df;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Modal Styles */
.modal-content {
  border: none;
  border-radius: 10px;
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
  border-bottom: 1px solid #e3e6f0;
  background-color: #f8f9fc;
  border-radius: 10px 10px 0 0;
}

.modal-footer {
  border-top: 1px solid #e3e6f0;
  background-color: #f8f9fc;
  border-radius: 0 0 10px 10px;
}

/* Loading Spinner */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Empty State */
.text-center.text-muted {
  color: #858796 !important;
}

.text-center.text-muted i {
  color: #d1d3e2 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .reglement-header {
    padding: 1rem;
    text-align: center;
  }

  .card-body {
    padding: 1rem;
  }

  .table-responsive {
    font-size: 0.8rem;
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    margin-bottom: 0.25rem;
  }
}

/* Animation for buttons */
.btn {
  transition: all 0.2s ease-in-out;
}

.btn:hover {
  transform: translateY(-1px);
}

/* Custom scrollbar */
.table-responsive::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Alert styles */
.alert {
  border: none;
  border-radius: 10px;
}

/* Shadow utilities */
.shadow {
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}