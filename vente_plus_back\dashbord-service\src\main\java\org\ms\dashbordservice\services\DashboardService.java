package org.ms.dashbordservice.services;

import org.ms.dashbordservice.dto.*;

import java.util.List;

public interface DashboardService {
    
    // Analytics par client
    ClientAnalyticsDTO getClientAnalytics(Long clientId);
    List<ClientAnalyticsDTO> getAllClientsAnalytics();
    
    // Clients les plus fidèles
    List<ClientFideliteDTO> getClientsPlusFideles(int limit);
    
    // Produits les plus vendus
    List<ProduitPopulariteDTO> getProduitsPlusVendus(int limit);
    List<ProduitPopulariteDTO> getProduitsPlusVendusParAnnee(int annee, int limit);
    
    // Produits en rupture de stock
    List<ProduitStockDTO> getProduitsEnRuptureStock();
    
    // Factures réglées et non réglées
    List<FactureStatusDTO> getFacturesReglees();
    List<FactureStatusDTO> getFacturesNonReglees();
    List<FactureStatusDTO> getFacturesPartiellemementReglees();
    
    // Dettes par client
    List<DetteClientDTO> getDettesByClient();
    DetteClientDTO getDetteByClient(Long clientId);
}
