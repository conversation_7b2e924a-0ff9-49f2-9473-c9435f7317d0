package org.ms.dashbordservice.web;

import lombok.extern.slf4j.Slf4j;
import org.ms.dashbordservice.dto.*;
import org.ms.dashbordservice.services.DashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/dashboard")
@Slf4j
public class DashboardRestController {

    @Autowired
    private DashboardService dashboardService;

    // Analytics par client
    @GetMapping("/client/{clientId}/analytics")
    public ResponseEntity<ClientAnalyticsDTO> getClientAnalytics(@PathVariable Long clientId) {
        log.info("Récupération des analytics pour le client: {}", clientId);
        ClientAnalyticsDTO analytics = dashboardService.getClientAnalytics(clientId);
        return ResponseEntity.ok(analytics);
    }

    @GetMapping("/clients/analytics")
    public ResponseEntity<List<ClientAnalyticsDTO>> getAllClientsAnalytics() {
        log.info("Récupération des analytics pour tous les clients");
        List<ClientAnalyticsDTO> analytics = dashboardService.getAllClientsAnalytics();
        return ResponseEntity.ok(analytics);
    }

    // Clients les plus fidèles
    @GetMapping("/clients/fideles")
    public ResponseEntity<List<ClientFideliteDTO>> getClientsPlusFideles(
            @RequestParam(defaultValue = "10") int limit) {
        log.info("Récupération des {} clients les plus fidèles", limit);
        List<ClientFideliteDTO> clientsFideles = dashboardService.getClientsPlusFideles(limit);
        return ResponseEntity.ok(clientsFideles);
    }

    // Produits les plus vendus
    @GetMapping("/produits/plus-vendus")
    public ResponseEntity<List<ProduitPopulariteDTO>> getProduitsPlusVendus(
            @RequestParam(defaultValue = "10") int limit) {
        log.info("Récupération des {} produits les plus vendus", limit);
        List<ProduitPopulariteDTO> produits = dashboardService.getProduitsPlusVendus(limit);
        return ResponseEntity.ok(produits);
    }

    @GetMapping("/produits/plus-vendus/annee/{annee}")
    public ResponseEntity<List<ProduitPopulariteDTO>> getProduitsPlusVendusParAnnee(
            @PathVariable int annee,
            @RequestParam(defaultValue = "10") int limit) {
        log.info("Récupération des {} produits les plus vendus pour l'année {}", limit, annee);
        List<ProduitPopulariteDTO> produits = dashboardService.getProduitsPlusVendusParAnnee(annee, limit);
        return ResponseEntity.ok(produits);
    }

    // Produits en rupture de stock
    @GetMapping("/produits/rupture-stock")
    public ResponseEntity<List<ProduitStockDTO>> getProduitsEnRuptureStock() {
        log.info("Récupération des produits en rupture de stock");
        List<ProduitStockDTO> produits = dashboardService.getProduitsEnRuptureStock();
        return ResponseEntity.ok(produits);
    }

    // Factures réglées et non réglées
    @GetMapping("/factures/reglees")
    public ResponseEntity<List<FactureStatusDTO>> getFacturesReglees() {
        log.info("Récupération des factures réglées");
        List<FactureStatusDTO> factures = dashboardService.getFacturesReglees();
        return ResponseEntity.ok(factures);
    }

    @GetMapping("/factures/non-reglees")
    public ResponseEntity<List<FactureStatusDTO>> getFacturesNonReglees() {
        log.info("Récupération des factures non réglées");
        List<FactureStatusDTO> factures = dashboardService.getFacturesNonReglees();
        return ResponseEntity.ok(factures);
    }

    @GetMapping("/factures/partiellement-reglees")
    public ResponseEntity<List<FactureStatusDTO>> getFacturesPartiellemementReglees() {
        log.info("Récupération des factures partiellement réglées");
        List<FactureStatusDTO> factures = dashboardService.getFacturesPartiellemementReglees();
        return ResponseEntity.ok(factures);
    }

    // Dettes par client
    @GetMapping("/dettes")
    public ResponseEntity<List<DetteClientDTO>> getDettesByClient() {
        log.info("Récupération des dettes par client");
        List<DetteClientDTO> dettes = dashboardService.getDettesByClient();
        return ResponseEntity.ok(dettes);
    }

    @GetMapping("/client/{clientId}/dettes")
    public ResponseEntity<DetteClientDTO> getDetteByClient(@PathVariable Long clientId) {
        log.info("Récupération des dettes pour le client: {}", clientId);
        DetteClientDTO dette = dashboardService.getDetteByClient(clientId);
        return ResponseEntity.ok(dette);
    }
}
