package org.ms.dashbordservice.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProduitStockDTO {
    private Long produitId;
    private String nomProduit;
    private String referenceProduit;
    private long quantiteEnStock;
    private BigDecimal prix;
    private boolean enRuptureStock;
    private String nomCategorie;
}
