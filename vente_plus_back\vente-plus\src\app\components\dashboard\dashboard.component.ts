import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';
import { DashboardService } from '../../services/dashboard.service';
import { ReglementService } from '../../services/reglement.service';
import {
  ClientAnalytics,
  ProduitPopularite,
  FactureStatus,
  DashboardStats,
  Client,
  Produit
} from '../../models/dashboard.model';

Chart.register(...registerables);

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class DashboardComponent implements OnInit {
  // Dashboard Stats
  stats: DashboardStats = {
    totalClients: 0,
    totalRevenue: 0,
    totalInvoices: 0,
    totalProducts: 0,
    outOfStockProducts: 0,
    unpaidInvoices: 0,
    partiallyPaidInvoices: 0,
    totalDebts: 0
  };

  // Data Arrays
  clientAnalytics: ClientAnalytics[] = [];
  bestSellingProducts: ProduitPopularite[] = [];
  loyalClients: Client[] = [];
  outOfStockProducts: Produit[] = [];
  unpaidInvoices: FactureStatus[] = [];
  partiallyPaidInvoices: FactureStatus[] = [];
  clientDebts: {[key: string]: number} = {};

  // Charts
  revenueChart: Chart | null = null;
  productChart: Chart | null = null;
  paymentChart: Chart | null = null;
  clientChart: Chart | null = null;

  // Loading states
  loading = true;
  error: string | null = null;

  constructor(
    private dashboardService: DashboardService,
    private reglementService: ReglementService
  ) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    // Destroy charts to prevent memory leaks
    if (this.revenueChart) this.revenueChart.destroy();
    if (this.productChart) this.productChart.destroy();
    if (this.paymentChart) this.paymentChart.destroy();
    if (this.clientChart) this.clientChart.destroy();
  }

  async loadDashboardData(): Promise<void> {
    try {
      this.loading = true;
      this.error = null;

      // Load all dashboard data in parallel
      const [
        clientAnalytics,
        bestProducts,
        loyalClients,
        outOfStock,
        unpaidInvoices,
        partialInvoices,
        debts
      ] = await Promise.all([
        this.dashboardService.getAllClientsAnalytics().toPromise(),
        this.dashboardService.getBestSellingProducts(10).toPromise(),
        this.dashboardService.getMostLoyalClients(5).toPromise(),
        this.dashboardService.getOutOfStockProducts().toPromise(),
        this.dashboardService.getUnpaidInvoices().toPromise(),
        this.dashboardService.getPartiallyPaidInvoices().toPromise(),
        this.dashboardService.getAllClientDebts().toPromise()
      ]);

      // Assign data
      this.clientAnalytics = clientAnalytics || [];
      this.bestSellingProducts = bestProducts || [];
      this.loyalClients = loyalClients || [];
      this.outOfStockProducts = outOfStock || [];
      this.unpaidInvoices = unpaidInvoices || [];
      this.partiallyPaidInvoices = partialInvoices || [];
      this.clientDebts = debts || {};

      // Calculate stats
      this.calculateStats();

      // Create charts
      setTimeout(() => {
        this.createCharts();
      }, 100);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      this.error = 'Erreur lors du chargement des données du tableau de bord';
    } finally {
      this.loading = false;
    }
  }

  private calculateStats(): void {
    this.stats = {
      totalClients: this.clientAnalytics.length,
      totalRevenue: this.clientAnalytics.reduce((sum, c) => sum + c.chiffresAffairesTotal, 0),
      totalInvoices: this.clientAnalytics.reduce((sum, c) => sum + c.nombreFacturesReglees + c.nombreFacturesNonReglees, 0),
      totalProducts: this.bestSellingProducts.length,
      outOfStockProducts: this.outOfStockProducts.length,
      unpaidInvoices: this.unpaidInvoices.length,
      partiallyPaidInvoices: this.partiallyPaidInvoices.length,
      totalDebts: Object.values(this.clientDebts).reduce((sum, debt) => sum + debt, 0)
    };
  }

  private createCharts(): void {
    this.createRevenueChart();
    this.createProductChart();
    this.createPaymentChart();
    this.createClientChart();
  }

  private createRevenueChart(): void {
    const canvas = document.getElementById('revenueChart') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Destroy existing chart
    if (this.revenueChart) {
      this.revenueChart.destroy();
    }

    const labels = this.clientAnalytics.map(c => `${c.client.prenom} ${c.client.nom}`);
    const data = this.clientAnalytics.map(c => c.chiffresAffairesTotal);

    this.revenueChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: 'Chiffre d\'affaires (€)',
          data: data,
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        plugins: {
          title: {
            display: true,
            text: 'Chiffre d\'affaires par client'
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return value + ' €';
              }
            }
          }
        }
      }
    });
  }

  private createProductChart(): void {
    const canvas = document.getElementById('productChart') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    if (this.productChart) {
      this.productChart.destroy();
    }

    const labels = this.bestSellingProducts.map(p => p.produit.nom);
    const data = this.bestSellingProducts.map(p => p.quantiteVendue);

    this.productChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: labels,
        datasets: [{
          label: 'Quantité vendue',
          data: data,
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 205, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        plugins: {
          title: {
            display: true,
            text: 'Produits les plus vendus'
          }
        }
      }
    });
  }

  private createPaymentChart(): void {
    const canvas = document.getElementById('paymentChart') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    if (this.paymentChart) {
      this.paymentChart.destroy();
    }

    const paidCount = this.clientAnalytics.reduce((sum, c) => sum + c.nombreFacturesReglees, 0);
    const unpaidCount = this.unpaidInvoices.length;
    const partialCount = this.partiallyPaidInvoices.length;

    this.paymentChart = new Chart(ctx, {
      type: 'pie',
      data: {
        labels: ['Factures payées', 'Factures impayées', 'Factures partielles'],
        datasets: [{
          data: [paidCount, unpaidCount, partialCount],
          backgroundColor: [
            'rgba(75, 192, 192, 0.6)',
            'rgba(255, 99, 132, 0.6)',
            'rgba(255, 205, 86, 0.6)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        plugins: {
          title: {
            display: true,
            text: 'Statut des factures'
          }
        }
      }
    });
  }

  private createClientChart(): void {
    const canvas = document.getElementById('clientChart') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    if (this.clientChart) {
      this.clientChart.destroy();
    }

    const labels = this.loyalClients.map(c => `${c.prenom} ${c.nom}`);
    const data = this.clientAnalytics
      .filter(ca => this.loyalClients.some(lc => lc.id === ca.client.id))
      .map(ca => ca.nombreCommandes);

    this.clientChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'Nombre de commandes',
          data: data,
          borderColor: 'rgba(153, 102, 255, 1)',
          backgroundColor: 'rgba(153, 102, 255, 0.2)',
          borderWidth: 2,
          fill: true
        }]
      },
      options: {
        responsive: true,
        plugins: {
          title: {
            display: true,
            text: 'Clients les plus fidèles'
          }
        },
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }

  // Helper methods for template
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  }

  getClientName(clientKey: string): string {
    // Extract client name from the key format
    const match = clientKey.match(/nom=([^,]+), prenom=([^,]+)/);
    if (match) {
      return `${match[2]} ${match[1]}`;
    }
    return clientKey;
  }

  refreshData(): void {
    this.loadDashboardData();
  }
}
