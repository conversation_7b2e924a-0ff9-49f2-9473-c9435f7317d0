package org.ms.dashbordservice.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClientFideliteDTO {
    private Long clientId;
    private String nomClient;
    private String prenomClient;
    private String emailClient;
    private int nombreCommandes;
    private BigDecimal chiffresAffairesTotal;
    private BigDecimal montantMoyenCommande;
    private LocalDateTime datePremiereCommande;
    private LocalDateTime dateDerniereCommande;
    private double scoreFidelite; // Calculé basé sur plusieurs critères
}
