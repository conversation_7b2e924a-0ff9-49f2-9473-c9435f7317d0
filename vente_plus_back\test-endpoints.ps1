# PowerShell script to test all endpoints
Write-Host "=== Testing Reglement and Dashboard Services ===" -ForegroundColor Green

# Function to make HTTP requests and display results
function Test-Endpoint {
    param(
        [string]$Method,
        [string]$Url,
        [string]$Body = $null,
        [string]$Description
    )
    
    Write-Host "`n--- $Description ---" -ForegroundColor Yellow
    Write-Host "$Method $Url" -ForegroundColor Cyan
    
    try {
        if ($Body) {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Body $Body -ContentType "application/json"
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method
        }
        
        Write-Host "✅ Success:" -ForegroundColor Green
        $response | ConvertTo-Json -Depth 3 | Write-Host
    }
    catch {
        Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🔧 Creating Test Data..." -ForegroundColor Magenta

# Create Clients
Test-Endpoint -Method "POST" -Url "http://localhost:8081/clients" -Description "Create Client 1" -Body '{
    "nom": "Dupont",
    "prenom": "<PERSON>",
    "email": "<EMAIL>",
    "telephone": "0123456789",
    "codeClient": "CLI001",
    "adresse": "123 Rue de la Paix, Paris"
}'

Test-Endpoint -Method "POST" -Url "http://localhost:8081/clients" -Description "Create Client 2" -Body '{
    "nom": "Martin",
    "prenom": "Marie",
    "email": "<EMAIL>",
    "telephone": "0987654321",
    "codeClient": "CLI002",
    "adresse": "456 Avenue des Champs, Lyon"
}'

# Create Products (assuming categories exist)
Test-Endpoint -Method "POST" -Url "http://localhost:8082/produits" -Description "Create Product 1" -Body '{
    "nom": "Smartphone Samsung",
    "prix": 599.99,
    "quantite": 50,
    "reference": "SAMS001",
    "categorieId": 1
}'

Test-Endpoint -Method "POST" -Url "http://localhost:8082/produits" -Description "Create Product 2" -Body '{
    "nom": "Laptop Dell",
    "prix": 899.99,
    "quantite": 0,
    "reference": "DELL001",
    "categorieId": 1
}'

Write-Host "`n⏳ Waiting 2 seconds for data to be created..." -ForegroundColor Yellow
Start-Sleep -Seconds 2

Write-Host "`n🧪 Testing Reglement Service Endpoints..." -ForegroundColor Magenta

# Test basic reglement endpoints
Test-Endpoint -Method "GET" -Url "http://localhost:8088/reglements" -Description "Get All Payments"

# Create a test payment (assuming invoice ID 1 exists)
Test-Endpoint -Method "POST" -Url "http://localhost:8088/reglements" -Description "Create Payment" -Body '{
    "factureId": 1,
    "montantPaye": 500.00,
    "modeReglement": "VIREMENT",
    "reference": "TEST001",
    "commentaire": "Test payment"
}'

# Test analytics endpoints
Test-Endpoint -Method "GET" -Url "http://localhost:8088/reglements/total/client/1" -Description "Total Payments by Client 1"
Test-Endpoint -Method "GET" -Url "http://localhost:8088/reglements/factures/non-reglees" -Description "Unpaid Invoices"
Test-Endpoint -Method "GET" -Url "http://localhost:8088/reglements/factures/partiellement-reglees" -Description "Partially Paid Invoices"

Write-Host "`n📊 Testing Dashboard Service Endpoints..." -ForegroundColor Magenta

# Test dashboard endpoints
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/clients/analytics" -Description "All Clients Analytics"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/client/1/analytics" -Description "Client 1 Analytics"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/clients/fideles?limit=5" -Description "Most Loyal Clients"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/produits/plus-vendus?limit=10" -Description "Best Selling Products"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/produits/rupture-stock" -Description "Out of Stock Products"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/factures/reglees" -Description "Paid Invoices"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/factures/non-reglees" -Description "Unpaid Invoices"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/dettes" -Description "Client Debts"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/client/1/chiffres-affaires" -Description "Client 1 Revenue"

Write-Host "`n✅ Testing Complete!" -ForegroundColor Green
Write-Host "Check the results above to see if all endpoints are working correctly." -ForegroundColor White
