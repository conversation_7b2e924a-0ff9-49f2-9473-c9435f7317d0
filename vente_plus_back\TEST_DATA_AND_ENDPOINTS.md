# Test Data and Endpoints Guide

## Prerequisites
Make sure all services are running:
- Eureka Discovery Service (port 8761)
- Config Service (port 5555)
- Client Service (port 8081)
- Produit Service (port 8082)
- Facture Service (port 8084)
- Reglement Service (port 8088)
- Dashboard Service (port 8089)

## 1. Create Test Data

### Step 1: Create Categories
```bash
# POST http://localhost:8083/categories
curl -X POST http://localhost:8083/categories \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Électronique",
    "description": "Produits électroniques et high-tech"
  }'

curl -X POST http://localhost:8083/categories \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Vêtements",
    "description": "Vêtements et accessoires"
  }'
```

### Step 2: Create Clients
```bash
# POST http://localhost:8081/clients
curl -X POST http://localhost:8081/clients \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "<PERSON><PERSON>",
    "prenom": "<PERSON>",
    "email": "<EMAIL>",
    "telephone": "0123456789",
    "codeClient": "CLI001",
    "adresse": "123 Rue de la Paix, Paris"
  }'

curl -X POST http://localhost:8081/clients \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Martin",
    "prenom": "Marie",
    "email": "<EMAIL>",
    "telephone": "0987654321",
    "codeClient": "CLI002",
    "adresse": "456 Avenue des Champs, Lyon"
  }'

curl -X POST http://localhost:8081/clients \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Bernard",
    "prenom": "Pierre",
    "email": "<EMAIL>",
    "telephone": "0147258369",
    "codeClient": "CLI003",
    "adresse": "789 Boulevard Saint-Germain, Marseille"
  }'
```

### Step 3: Create Products
```bash
# POST http://localhost:8082/produits
curl -X POST http://localhost:8082/produits \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Smartphone Samsung",
    "prix": 599.99,
    "quantite": 50,
    "reference": "SAMS001",
    "categorieId": 1
  }'

curl -X POST http://localhost:8082/produits \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Laptop Dell",
    "prix": 899.99,
    "quantite": 25,
    "reference": "DELL001",
    "categorieId": 1
  }'

curl -X POST http://localhost:8082/produits \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "T-shirt Nike",
    "prix": 29.99,
    "quantite": 0,
    "reference": "NIKE001",
    "categorieId": 2
  }'

curl -X POST http://localhost:8082/produits \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Jeans Levis",
    "prix": 79.99,
    "quantite": 30,
    "reference": "LEVI001",
    "categorieId": 2
  }'
```

### Step 4: Create Devis (Quotes)
```bash
# POST http://localhost:8085/devis
curl -X POST http://localhost:8085/devis \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": 1,
    "dateValidite": "2024-12-31",
    "lignes": [
      {
        "produitId": 1,
        "quantite": 2,
        "prixUnitaire": 599.99,
        "remise": 0,
        "tva": 20
      },
      {
        "produitId": 2,
        "quantite": 1,
        "prixUnitaire": 899.99,
        "remise": 50,
        "tva": 20
      }
    ]
  }'

curl -X POST http://localhost:8085/devis \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": 2,
    "dateValidite": "2024-12-31",
    "lignes": [
      {
        "produitId": 3,
        "quantite": 3,
        "prixUnitaire": 29.99,
        "remise": 0,
        "tva": 20
      },
      {
        "produitId": 4,
        "quantite": 2,
        "prixUnitaire": 79.99,
        "remise": 10,
        "tva": 20
      }
    ]
  }'

curl -X POST http://localhost:8085/devis \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": 3,
    "dateValidite": "2024-12-31",
    "lignes": [
      {
        "produitId": 1,
        "quantite": 1,
        "prixUnitaire": 599.99,
        "remise": 0,
        "tva": 20
      }
    ]
  }'
```

### Step 5: Validate Devis and Create Invoices
```bash
# Validate devis first
curl -X PUT http://localhost:8085/devis/1/valider
curl -X PUT http://localhost:8085/devis/2/valider
curl -X PUT http://localhost:8085/devis/3/valider

# Create invoices from devis
curl -X POST http://localhost:8084/factures/from-devis/1
curl -X POST http://localhost:8084/factures/from-devis/2
curl -X POST http://localhost:8084/factures/from-devis/3
```

### Step 6: Create Payments (Reglements)
```bash
# POST http://localhost:8088/reglements
# Payment for invoice 1 (partial payment)
curl -X POST http://localhost:8088/reglements \
  -H "Content-Type: application/json" \
  -d '{
    "factureId": 1,
    "montantPaye": 1000.00,
    "modeReglement": "VIREMENT",
    "reference": "VIR001",
    "commentaire": "Paiement partiel facture 1"
  }'

# Payment for invoice 1 (remaining amount)
curl -X POST http://localhost:8088/reglements \
  -H "Content-Type: application/json" \
  -d '{
    "factureId": 1,
    "montantPaye": 1099.97,
    "modeReglement": "CHEQUE",
    "reference": "CHQ001",
    "commentaire": "Solde facture 1"
  }'

# Payment for invoice 2 (partial payment)
curl -X POST http://localhost:8088/reglements \
  -H "Content-Type: application/json" \
  -d '{
    "factureId": 2,
    "montantPaye": 100.00,
    "modeReglement": "ESPECES",
    "reference": "ESP001",
    "commentaire": "Acompte facture 2"
  }'

# No payment for invoice 3 (unpaid)
```

## 2. Test Reglement Service Endpoints

### Basic CRUD Operations
```bash
# Get all payments
curl -X GET http://localhost:8088/reglements

# Get payment by ID
curl -X GET http://localhost:8088/reglements/1

# Get payments by client
curl -X GET http://localhost:8088/reglements/client/1

# Get payments by invoice
curl -X GET http://localhost:8088/reglements/facture/1

# Get payments by payment mode
curl -X GET http://localhost:8088/reglements/mode/VIREMENT
```

### Analytics Endpoints
```bash
# Total payments by client
curl -X GET http://localhost:8088/reglements/total/client/1

# Total payments by client and year
curl -X GET http://localhost:8088/reglements/total/client/1/annee/2024

# Total payments by invoice
curl -X GET http://localhost:8088/reglements/total/facture/1

# Remaining amount for invoice
curl -X GET http://localhost:8088/reglements/restant/facture/1
curl -X GET http://localhost:8088/reglements/restant/facture/2
curl -X GET http://localhost:8088/reglements/restant/facture/3

# Check if invoice is fully paid
curl -X GET http://localhost:8088/reglements/facture/1/payee
curl -X GET http://localhost:8088/reglements/facture/2/payee
curl -X GET http://localhost:8088/reglements/facture/3/payee

# Get unpaid invoices
curl -X GET http://localhost:8088/reglements/factures/non-reglees

# Get partially paid invoices
curl -X GET http://localhost:8088/reglements/factures/partiellement-reglees
```

## 3. Test Dashboard Service Endpoints

### Client Analytics
```bash
# Get analytics for specific client
curl -X GET http://localhost:8089/dashboard/client/1/analytics
curl -X GET http://localhost:8089/dashboard/client/2/analytics
curl -X GET http://localhost:8089/dashboard/client/3/analytics

# Get analytics for all clients
curl -X GET http://localhost:8089/dashboard/clients/analytics

# Get most loyal clients
curl -X GET http://localhost:8089/dashboard/clients/fideles?limit=5
```

### Product Analytics
```bash
# Get best-selling products
curl -X GET http://localhost:8089/dashboard/produits/plus-vendus?limit=10

# Get best-selling products by year
curl -X GET http://localhost:8089/dashboard/produits/plus-vendus/annee/2024?limit=10

# Get out-of-stock products
curl -X GET http://localhost:8089/dashboard/produits/rupture-stock
```

### Invoice Status
```bash
# Get paid invoices
curl -X GET http://localhost:8089/dashboard/factures/reglees

# Get unpaid invoices
curl -X GET http://localhost:8089/dashboard/factures/non-reglees

# Get partially paid invoices
curl -X GET http://localhost:8089/dashboard/factures/partiellement-reglees
```

### Debt Management
```bash
# Get all client debts
curl -X GET http://localhost:8089/dashboard/dettes

# Get debt for specific client
curl -X GET http://localhost:8089/dashboard/client/1/dettes
curl -X GET http://localhost:8089/dashboard/client/2/dettes
curl -X GET http://localhost:8089/dashboard/client/3/dettes
```

### Revenue Analytics
```bash
# Get client revenue
curl -X GET http://localhost:8089/dashboard/client/1/chiffres-affaires
curl -X GET http://localhost:8089/dashboard/client/2/chiffres-affaires

# Get client revenue by year
curl -X GET http://localhost:8089/dashboard/client/1/chiffres-affaires/annee/2024
curl -X GET http://localhost:8089/dashboard/client/2/chiffres-affaires/annee/2024
```

## Expected Results Summary

After running all the test data creation:
- **3 clients** created
- **4 products** created (1 out of stock)
- **3 quotes** created and validated
- **3 invoices** created
- **3 payments** created (invoice 1 fully paid, invoice 2 partially paid, invoice 3 unpaid)

The dashboard should show:
- Client 1: Fully paid invoice, high revenue
- Client 2: Partially paid invoice, medium revenue  
- Client 3: Unpaid invoice, debt exists
- 1 product out of stock (T-shirt Nike)
- Various analytics and loyalty scores
