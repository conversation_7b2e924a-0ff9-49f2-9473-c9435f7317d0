/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\.?/, '').length;
    if (i === 1 && v === 0)
        return 1;
    return 5;
}
export default ["gl", [["a.m.", "p.m."], u, u], u, [["d.", "l.", "m.", "m.", "x.", "v.", "s."], ["dom.", "luns", "mar.", "mér.", "xov.", "ven.", "sáb."], ["domingo", "luns", "martes", "mércores", "xoves", "venres", "s<PERSON>bado"], ["do.", "lu.", "ma.", "mé.", "xo.", "ve.", "sá."]], [["D", "L", "M", "M", "X", "V", "S"], ["Dom.", "Luns", "Mar.", "Mér.", "Xov.", "Ven.", "S<PERSON>b."], ["<PERSON>", "Luns", "<PERSON>es", "M<PERSON>rcores", "<PERSON>oves", "Venres", "S<PERSON>bado"], ["Do", "Lu", "Ma", "Mé", "Xo", "Ve", "Sá"]], [["x.", "f.", "m.", "a.", "m.", "x.", "x.", "a.", "s.", "o.", "n.", "d."], ["xan.", "feb.", "mar.", "abr.", "maio", "xuño", "xul.", "ago.", "set.", "out.", "nov.", "dec."], ["xaneiro", "febreiro", "marzo", "abril", "maio", "xuño", "xullo", "agosto", "setembro", "outubro", "novembro", "decembro"]], [["X", "F", "M", "A", "M", "X", "X", "A", "S", "O", "N", "D"], ["Xan.", "Feb.", "Mar.", "Abr.", "Maio", "Xuño", "Xul.", "Ago.", "Set.", "Out.", "Nov.", "Dec."], ["Xaneiro", "Febreiro", "Marzo", "Abril", "Maio", "Xuño", "Xullo", "Agosto", "Setembro", "Outubro", "Novembro", "Decembro"]], [["a.C.", "d.C."], u, ["antes de Cristo", "despois de Cristo"]], 1, [6, 0], ["dd/MM/yy", "d 'de' MMM 'de' y", "d 'de' MMMM 'de' y", "EEEE, d 'de' MMMM 'de' y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{0}, {1}", u, "{0} 'do' {1}", u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "EUR", "€", "euro", { "BYN": [u, "Br"], "ESP": ["₧"], "JPY": ["JP¥", "¥"], "KMF": [u, "FC"], "MXN": ["$MX", "$"], "PHP": [u, "₱"], "RUB": [u, "руб"], "THB": ["฿"], "TWD": ["NT$"], "XCD": [u, "$"] }, "ltr", plural];
//# sourceMappingURL=gl.js.map