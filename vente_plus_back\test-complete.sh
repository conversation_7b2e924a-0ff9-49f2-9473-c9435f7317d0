#!/bin/bash

# Complete Test Script for Reglement and Dashboard Services
echo "=== Complete Test Suite for Reglement & Dashboard Services ==="

# Function to test endpoints
test_endpoint() {
    local method=$1
    local url=$2
    local description=$3
    local body=$4
    
    echo ""
    echo "--- $description ---"
    echo "$method $url"
    
    if [ -n "$body" ]; then
        response=$(curl -s -X $method "$url" \
            -H "Content-Type: application/json" \
            -d "$body")
    else
        response=$(curl -s -X $method "$url")
    fi
    
    if [ $? -eq 0 ]; then
        echo "✅ Success:"
        echo "$response" | jq . 2>/dev/null || echo "$response"
    else
        echo "❌ Error: Failed to connect"
    fi
}

echo ""
echo "🔧 Step 1: Creating Test Data..."

# Create Categories
echo ""
echo "📁 Creating Categories..."
test_endpoint "POST" "http://localhost:8083/categories" "Create Electronics Category" '{
    "nom": "Électronique",
    "description": "Produits électroniques et high-tech"
}'

test_endpoint "POST" "http://localhost:8083/categories" "Create Clothing Category" '{
    "nom": "Vêtements",
    "description": "Vêtements et accessoires"
}'

# Create Clients
echo ""
echo "👥 Creating Clients..."
test_endpoint "POST" "http://localhost:8081/clients" "Create Client 1 - Jean Dupont" '{
    "nom": "Dupont",
    "prenom": "Jean",
    "email": "<EMAIL>",
    "telephone": "0123456789",
    "codeClient": "CLI001",
    "adresse": "123 Rue de la Paix, Paris"
}'

test_endpoint "POST" "http://localhost:8081/clients" "Create Client 2 - Marie Martin" '{
    "nom": "Martin",
    "prenom": "Marie",
    "email": "<EMAIL>",
    "telephone": "0987654321",
    "codeClient": "CLI002",
    "adresse": "456 Avenue des Champs, Lyon"
}'

test_endpoint "POST" "http://localhost:8081/clients" "Create Client 3 - Pierre Bernard" '{
    "nom": "Bernard",
    "prenom": "Pierre",
    "email": "<EMAIL>",
    "telephone": "0147258369",
    "codeClient": "CLI003",
    "adresse": "789 Boulevard Saint-Germain, Marseille"
}'

# Create Products
echo ""
echo "📦 Creating Products..."
test_endpoint "POST" "http://localhost:8082/produits" "Create Product 1 - Smartphone" '{
    "nom": "Smartphone Samsung",
    "prix": 599.99,
    "quantite": 50,
    "reference": "SAMS001",
    "categorieId": 1
}'

test_endpoint "POST" "http://localhost:8082/produits" "Create Product 2 - Laptop" '{
    "nom": "Laptop Dell",
    "prix": 899.99,
    "quantite": 25,
    "reference": "DELL001",
    "categorieId": 1
}'

test_endpoint "POST" "http://localhost:8082/produits" "Create Product 3 - T-shirt (Out of Stock)" '{
    "nom": "T-shirt Nike",
    "prix": 29.99,
    "quantite": 0,
    "reference": "NIKE001",
    "categorieId": 2
}'

test_endpoint "POST" "http://localhost:8082/produits" "Create Product 4 - Jeans" '{
    "nom": "Jeans Levis",
    "prix": 79.99,
    "quantite": 30,
    "reference": "LEVI001",
    "categorieId": 2
}'

echo ""
echo "⏳ Waiting 3 seconds for data to be created..."
sleep 3

# Create Devis
echo ""
echo "📋 Creating Devis (Quotes)..."
test_endpoint "POST" "http://localhost:8085/devis" "Create Devis 1 for Client 1" '{
    "clientId": 1,
    "dateValidite": "2024-12-31",
    "lignes": [
        {
            "produitId": 1,
            "quantite": 2,
            "prixUnitaire": 599.99,
            "remise": 0,
            "tva": 20
        },
        {
            "produitId": 2,
            "quantite": 1,
            "prixUnitaire": 899.99,
            "remise": 50,
            "tva": 20
        }
    ]
}'

test_endpoint "POST" "http://localhost:8085/devis" "Create Devis 2 for Client 2" '{
    "clientId": 2,
    "dateValidite": "2024-12-31",
    "lignes": [
        {
            "produitId": 3,
            "quantite": 3,
            "prixUnitaire": 29.99,
            "remise": 0,
            "tva": 20
        },
        {
            "produitId": 4,
            "quantite": 2,
            "prixUnitaire": 79.99,
            "remise": 10,
            "tva": 20
        }
    ]
}'

test_endpoint "POST" "http://localhost:8085/devis" "Create Devis 3 for Client 3" '{
    "clientId": 3,
    "dateValidite": "2024-12-31",
    "lignes": [
        {
            "produitId": 1,
            "quantite": 1,
            "prixUnitaire": 599.99,
            "remise": 0,
            "tva": 20
        }
    ]
}'

echo ""
echo "⏳ Waiting 2 seconds..."
sleep 2

# Validate Devis
echo ""
echo "✅ Validating Devis..."
test_endpoint "PUT" "http://localhost:8085/devis/1/valider" "Validate Devis 1"
test_endpoint "PUT" "http://localhost:8085/devis/2/valider" "Validate Devis 2"
test_endpoint "PUT" "http://localhost:8085/devis/3/valider" "Validate Devis 3"

echo ""
echo "⏳ Waiting 2 seconds..."
sleep 2

# Create Invoices
echo ""
echo "🧾 Creating Invoices from Devis..."
test_endpoint "POST" "http://localhost:8084/factures/from-devis/1" "Create Invoice from Devis 1"
test_endpoint "POST" "http://localhost:8084/factures/from-devis/2" "Create Invoice from Devis 2"
test_endpoint "POST" "http://localhost:8084/factures/from-devis/3" "Create Invoice from Devis 3"

echo ""
echo "⏳ Waiting 3 seconds for invoices to be created..."
sleep 3

echo ""
echo "💰 Step 2: Testing Reglement Service..."

# Create Payments
echo ""
echo "💳 Creating Payments..."
test_endpoint "POST" "http://localhost:8088/reglements" "Full Payment for Invoice 1" '{
    "factureId": 1,
    "montantPaye": 2099.97,
    "modeReglement": "VIREMENT",
    "reference": "VIR001",
    "commentaire": "Paiement complet facture 1"
}'

test_endpoint "POST" "http://localhost:8088/reglements" "Partial Payment for Invoice 2" '{
    "factureId": 2,
    "montantPaye": 100.00,
    "modeReglement": "CHEQUE",
    "reference": "CHQ001",
    "commentaire": "Acompte facture 2"
}'

# Test Reglement Endpoints
echo ""
echo "🧪 Testing Reglement Service Endpoints..."
test_endpoint "GET" "http://localhost:8088/reglements" "Get All Payments"
test_endpoint "GET" "http://localhost:8088/reglements/client/1" "Get Payments by Client 1"
test_endpoint "GET" "http://localhost:8088/reglements/total/client/1" "Total Payments by Client 1"
test_endpoint "GET" "http://localhost:8088/reglements/factures/non-reglees" "Unpaid Invoices"
test_endpoint "GET" "http://localhost:8088/reglements/factures/partiellement-reglees" "Partially Paid Invoices"

echo ""
echo "📊 Step 3: Testing Dashboard Service..."

# Test Dashboard Analytics
echo ""
echo "📈 Testing Client Analytics..."
test_endpoint "GET" "http://localhost:8089/dashboard/client/1/analytics" "Client 1 Complete Analytics"
test_endpoint "GET" "http://localhost:8089/dashboard/client/2/analytics" "Client 2 Complete Analytics"
test_endpoint "GET" "http://localhost:8089/dashboard/clients/analytics" "All Clients Analytics"

echo ""
echo "🏆 Testing Loyalty Analytics..."
test_endpoint "GET" "http://localhost:8089/dashboard/clients/fideles?limit=5" "Most Loyal Clients"

echo ""
echo "📦 Testing Product Analytics..."
test_endpoint "GET" "http://localhost:8089/dashboard/produits/plus-vendus?limit=10" "Best Selling Products"
test_endpoint "GET" "http://localhost:8089/dashboard/produits/rupture-stock" "Out of Stock Products"

echo ""
echo "🧾 Testing Invoice Status..."
test_endpoint "GET" "http://localhost:8089/dashboard/factures/reglees" "Paid Invoices"
test_endpoint "GET" "http://localhost:8089/dashboard/factures/non-reglees" "Unpaid Invoices"
test_endpoint "GET" "http://localhost:8089/dashboard/factures/partiellement-reglees" "Partially Paid Invoices"

echo ""
echo "💸 Testing Debt Management..."
test_endpoint "GET" "http://localhost:8089/dashboard/dettes" "All Client Debts"
test_endpoint "GET" "http://localhost:8089/dashboard/client/1/dettes" "Client 1 Debt"
test_endpoint "GET" "http://localhost:8089/dashboard/client/2/dettes" "Client 2 Debt"

echo ""
echo "💰 Testing Revenue Analytics..."
test_endpoint "GET" "http://localhost:8089/dashboard/client/1/chiffres-affaires" "Client 1 Total Revenue"
test_endpoint "GET" "http://localhost:8089/dashboard/client/2/chiffres-affaires" "Client 2 Total Revenue"

echo ""
echo "🎉 Testing Complete!"
echo "All endpoints have been tested. Check the results above!"
