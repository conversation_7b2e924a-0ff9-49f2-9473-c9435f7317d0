export interface ClientAnalytics {
  client: Client;
  chiffresAffairesTotal: number;
  chiffresAffairesParAnnee: { [year: string]: number };
  montantNonPaye: number;
  nombreFacturesReglees: number;
  nombreFacturesNonReglees: number;
  nombreCommandes: number;
  montantMoyenCommande: number;
}

export interface Client {
  id: number;
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  codeClient: string;
  adresse: string;
  createdAt: Date;
}

export interface ProduitPopularite {
  produit: Produit;
  quantiteVendue: number;
  chiffresAffaires: number;
  nombreCommandes: number;
}

export interface Produit {
  id: number;
  nom: string;
  prix: number;
  quantite: number;
  reference: string;
  imageUrl?: string;
  createdAt: Date;
  categorieId: number;
  categorie?: any;
}

export interface FactureStatus {
  facture: Facture;
  client: Client;
  montantPaye: number;
  montantRestant: number;
  estCompletelyPayee: boolean;
}

export interface Facture {
  id: number;
  dateFacture: Date;
  montantHT: number;
  montantTVA: number;
  montantTTC: number;
  clientID: number;
  devisId: number;
  facturelignes: FactureLigne[];
}

export interface FactureLigne {
  id: number;
  produitID: number;
  quantite: number;
  prix: number;
  remise?: number;
  tva?: number;
  produit?: Produit;
}

export interface DashboardStats {
  totalClients: number;
  totalRevenue: number;
  totalInvoices: number;
  totalProducts: number;
  outOfStockProducts: number;
  unpaidInvoices: number;
  partiallyPaidInvoices: number;
  totalDebts: number;
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  fill?: boolean;
}

export interface RevenueByMonth {
  month: string;
  revenue: number;
}

export interface TopClient {
  client: Client;
  totalRevenue: number;
  totalOrders: number;
}

export interface ProductSales {
  product: Produit;
  totalSold: number;
  revenue: number;
}

export interface DebtInfo {
  client: Client;
  totalDebt: number;
  oldestInvoiceDate: Date;
}
