<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<launchConfiguration type="org.springframework.ide.eclipse.boot.launch">
    <listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_PATHS">
        <listEntry value="/authentification-service/src/main/java/org/ms/authentificationservice/AuthentificationServiceApplication.java"/>
    </listAttribute>
    <listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_TYPES">
        <listEntry value="1"/>
    </listAttribute>
    <booleanAttribute key="org.eclipse.jdt.launching.ATTR_EXCLUDE_TEST_CODE" value="true"/>
    <stringAttribute key="org.eclipse.jdt.launching.CLASSPATH_PROVIDER" value="org.springframework.ide.eclipse.boot.launch.BootMavenClassPathProvider"/>
    <stringAttribute key="org.eclipse.jdt.launching.MAIN_TYPE" value="org.ms.authentificationservice.AuthentificationServiceApplication"/>
    <stringAttribute key="org.eclipse.jdt.launching.PROJECT_ATTR" value="authentification-service"/>
    <stringAttribute key="org.eclipse.jdt.launching.SOURCE_PATH_PROVIDER" value="org.springframework.ide.eclipse.boot.launch.BootMavenSourcePathProvider"/>
    <stringAttribute key="process_factory_id" value="org.springframework.ide.eclipse.boot.launch.process.BootProcessFactory"/>
    <booleanAttribute key="spring.boot.auto-connect" value="true"/>
    <booleanAttribute key="spring.boot.jmx.enable" value="true"/>
    <booleanAttribute key="spring.boot.lifecycle.enable" value="true"/>
    <stringAttribute key="spring.boot.lifecycle.termination.timeout" value="15000"/>
    <booleanAttribute key="spring.boot.livebean.enable" value="false"/>
    <stringAttribute key="spring.boot.livebean.port" value="0"/>
</launchConfiguration>
