import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ReglementService } from '../../services/reglement.service';
import { ClientService } from '../../services/client.service';
import { Reglement, ReglementDTO, FactureStatus } from '../../models/reglement.model';
import { Client } from '../../models/dashboard.model';

declare var bootstrap: any;

@Component({
  selector: 'app-reglement',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './reglement.component.html',
  styleUrl: './reglement.component.css'
})
export class ReglementComponent implements OnInit {
  // Data Arrays
  reglements: Reglement[] = [];
  filteredReglements: Reglement[] = [];
  clients: Client[] = [];
  paidInvoices: FactureStatus[] = [];
  unpaidInvoices: FactureStatus[] = [];
  partialInvoices: FactureStatus[] = [];
  availableInvoices: FactureStatus[] = [];

  // Form
  reglementForm: FormGroup;
  isEditMode = false;
  currentReglement: Reglement | null = null;

  // Filters
  selectedClientId: string = '';
  selectedPaymentMode: string = '';
  selectedYear: string = '';
  paymentModes: string[] = [];
  years: number[] = [];

  // Stats
  totalPayments = 0;

  // Loading states
  loading = true;
  saving = false;
  error: string | null = null;

  // Modal
  private modal: any;

  constructor(
    private reglementService: ReglementService,
    private clientService: ClientService,
    private fb: FormBuilder
  ) {
    this.reglementForm = this.createForm();
    this.paymentModes = this.reglementService.getPaymentModes();
  }

  ngOnInit(): void {
    this.loadData();
    this.generateYears();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      factureId: ['', [Validators.required]],
      montantPaye: ['', [Validators.required, Validators.min(0.01)]],
      modeReglement: ['', [Validators.required]],
      reference: ['', [Validators.required]],
      commentaire: ['']
    });
  }

  async loadData(): Promise<void> {
    try {
      this.loading = true;
      this.error = null;

      // Load all data in parallel
      const [
        reglements,
        clients,
        paidInvoices,
        unpaidInvoices,
        partialInvoices
      ] = await Promise.all([
        this.reglementService.getAllReglements().toPromise(),
        this.clientService.getAllClients().toPromise(),
        this.reglementService.getFacturesReglees().toPromise(),
        this.reglementService.getFacturesNonReglees().toPromise(),
        this.reglementService.getFacturesPartiellemementReglees().toPromise()
      ]);

      // Assign data
      this.reglements = reglements || [];
      this.clients = clients || [];
      this.paidInvoices = paidInvoices || [];
      this.unpaidInvoices = unpaidInvoices || [];
      this.partialInvoices = partialInvoices || [];

      // Calculate stats
      this.calculateStats();

      // Apply filters
      this.applyFilters();

      // Prepare available invoices for form
      this.prepareAvailableInvoices();

    } catch (error) {
      console.error('Error loading reglement data:', error);
      this.error = 'Erreur lors du chargement des données';
    } finally {
      this.loading = false;
    }
  }

  private calculateStats(): void {
    this.totalPayments = this.reglements.reduce((sum, r) => sum + r.montantPaye, 0);
  }

  private prepareAvailableInvoices(): void {
    // Combine unpaid and partial invoices for the form
    this.availableInvoices = [...this.unpaidInvoices, ...this.partialInvoices];
  }

  private generateYears(): void {
    const currentYear = new Date().getFullYear();
    this.years = [];
    for (let i = currentYear; i >= currentYear - 5; i--) {
      this.years.push(i);
    }
  }

  // Filter Methods
  applyFilters(): void {
    this.filteredReglements = this.reglements.filter(reglement => {
      let matches = true;

      // Filter by client
      if (this.selectedClientId) {
        matches = matches && reglement.clientId?.toString() === this.selectedClientId;
      }

      // Filter by payment mode
      if (this.selectedPaymentMode) {
        matches = matches && reglement.modeReglement === this.selectedPaymentMode;
      }

      // Filter by year
      if (this.selectedYear) {
        const reglementYear = new Date(reglement.dateReglement || '').getFullYear();
        matches = matches && reglementYear.toString() === this.selectedYear;
      }

      return matches;
    });
  }

  clearFilters(): void {
    this.selectedClientId = '';
    this.selectedPaymentMode = '';
    this.selectedYear = '';
    this.applyFilters();
  }

  refreshData(): void {
    this.loadData();
  }

  // Modal Methods
  openCreateModal(): void {
    this.isEditMode = false;
    this.currentReglement = null;
    this.reglementForm.reset();
    this.showModal();
  }

  editReglement(reglement: Reglement): void {
    this.isEditMode = true;
    this.currentReglement = reglement;
    this.reglementForm.patchValue({
      factureId: reglement.factureId,
      montantPaye: reglement.montantPaye,
      modeReglement: reglement.modeReglement,
      reference: reglement.reference,
      commentaire: reglement.commentaire
    });
    this.showModal();
  }

  viewReglement(reglement: Reglement): void {
    // Implementation for viewing reglement details
    console.log('View reglement:', reglement);
  }

  async deleteReglement(reglement: Reglement): Promise<void> {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le règlement ${reglement.numeroReglement}?`)) {
      try {
        await this.reglementService.deleteReglement(reglement.id!).toPromise();
        this.loadData();
      } catch (error) {
        console.error('Error deleting reglement:', error);
        alert('Erreur lors de la suppression du règlement');
      }
    }
  }

  async saveReglement(): Promise<void> {
    if (this.reglementForm.invalid) return;

    try {
      this.saving = true;
      const formValue = this.reglementForm.value;

      if (this.isEditMode && this.currentReglement) {
        await this.reglementService.updateReglement(this.currentReglement.id!, formValue).toPromise();
      } else {
        await this.reglementService.createReglement(formValue).toPromise();
      }

      this.hideModal();
      this.loadData();
    } catch (error) {
      console.error('Error saving reglement:', error);
      alert('Erreur lors de la sauvegarde du règlement');
    } finally {
      this.saving = false;
    }
  }

  private showModal(): void {
    const modalElement = document.getElementById('reglementModal');
    if (modalElement) {
      const modal = new (window as any).bootstrap.Modal(modalElement);
      modal.show();
    }
  }

  private hideModal(): void {
    const modalElement = document.getElementById('reglementModal');
    if (modalElement) {
      const modal = (window as any).bootstrap.Modal.getInstance(modalElement);
      if (modal) {
        modal.hide();
      }
    }
  }

  // Helper Methods
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  }

  formatPaymentMode(mode: string): string {
    return this.reglementService.formatPaymentMode(mode);
  }

  getPaymentModeClass(mode: string): string {
    const classes: {[key: string]: string} = {
      'CARTE': 'bg-primary',
      'VIREMENT': 'bg-success',
      'ESPECES': 'bg-warning',
      'CHEQUE': 'bg-info'
    };
    return classes[mode] || 'bg-secondary';
  }

  getClientById(clientId?: number): any {
    if (!clientId) return null;
    return this.clients.find(c => c.id === clientId);
  }
}
