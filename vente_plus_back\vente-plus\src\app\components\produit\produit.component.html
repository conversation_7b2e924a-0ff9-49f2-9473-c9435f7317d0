<!-- Alerte animée -->
<div *ngIf="alertMessage" class="alert" [ngClass]="alertType" (click)="closeAlert()">
  {{ alertMessage }}
</div>

<div class="category-container">
  <div class="header">
    <div class="header-left">
      <h2>Gestion des Produits</h2>
      <p class="subtitle">Gérez vos produits et leurs catégories</p>
    </div>
    <button class="add-button" title="Ajouter un produit" (click)="openModal()">
      <i class="fa-solid fa-plus"></i>
    </button>
  </div>
  
  <div class="stats-container">
    <div class="stat-card">
      <div class="stat-icon">
        <i class="fa-solid fa-box"></i>
      </div>
      <div class="stat-info">
        <h3>{{produits.length}}</h3>
        <p>Total Produits</p>
      </div>
    </div>
  </div>

  <div class="table-container">
    <div class="table-header">
      <div class="search-box">
        <i class="fa-solid fa-search"></i>
        <input type="text" placeholder="Rechercher un produit..." [(ngModel)]="searchTerm" (ngModelChange)="onSearchChange()">
      </div>
    </div>
    <table>
      <thead>
        <tr>
          <th>Image</th>
          <th>Réference</th>
          <th>Nom</th>
          <th>Prix</th>
          <th>Quantité</th>
          <th>Catégorie</th>
          <th>Date de création</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prod of paginatedProduits">
          <td class="image-cell">
            <div class="product-image" *ngIf="prod.imageUrl">
              <img [src]="prod.imageUrl" [alt]="prod.nom" />
            </div>
            <div class="no-image" *ngIf="!prod.imageUrl">
              <i class="fa-solid fa-image"></i>
            </div>
          </td>
          <td>{{prod.reference}}</td>
          <td>{{prod.nom}}</td>
          <td>{{prod.prix | number:'1.2-2'}} €</td>
          <td>
            <div class="stock-info" [ngClass]="getStockStatusClass(prod.quantite)">
              <span class="stock-number">{{prod.quantite}}</span>
              <span class="stock-status">{{getStockStatusText(prod.quantite)}}</span>
              <i class="stock-icon" [ngClass]="getStockIconClass(prod.quantite)"></i>
            </div>
          </td>
          <td>{{prod.categorie?.nom}}</td>
          <td class="date-cell">
            <div class="date-info">
              <i class="fa-solid fa-calendar"></i>
              <span>{{prod.createdAt | date:'dd/MM/yyyy'}}</span>
            </div>
            <div class="time-info">
              <i class="fa-solid fa-clock"></i>
              <span>{{prod.createdAt | date:'HH:mm'}}</span>
            </div>
          </td>
          <td class="actions">
            <button class="edit-btn" title="Modifier" (click)="openEditModal(prod)">
              <i class="fa-solid fa-pen-to-square"></i>
            </button>
            <button class="delete-btn" title="Supprimer" (click)="openDeleteModal(prod)">
              <i class="fa-solid fa-trash"></i>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
    <!-- Pagination -->
    <div class="pagination" *ngIf="totalPages > 1">
      <button (click)="prevPage()" [disabled]="currentPage === 1">&laquo;</button>
      <button *ngFor="let page of [].constructor(totalPages); let i = index" (click)="goToPage(i+1)" [class.active]="currentPage === (i+1)">{{ i+1 }}</button>
      <button (click)="nextPage()" [disabled]="currentPage === totalPages">&raquo;</button>
    </div>
  </div>

  <!-- Modal d'ajout/modification de produit -->
  <div class="modal-backdrop" *ngIf="showModal" (click)="closeModal()"></div>
  <div class="modal" *ngIf="showModal">
    <form (ngSubmit)="onModalSubmit()" class="modal-content" (click)="$event.stopPropagation()">
      <h3>{{ isEditMode ? 'Modifier le produit' : 'Ajouter un produit' }}</h3>
      <div class="form-group">
        <input
          type="text"
          [(ngModel)]="modalProduit.nom"
          name="produitNom"
          placeholder="Nom du produit"
          required
          autofocus
        />
        <i class="fa-solid fa-box"></i>
      </div>
      <div class="form-group">
        <input
          type="text"
          [(ngModel)]="modalProduit.reference"
          name="produitReference"
          placeholder="Référence du produit"
          required
        />
        <i class="fa-solid fa-hashtag"></i>
      </div>
      <div class="form-group">
        <input
          type="number"
          [(ngModel)]="modalProduit.prix"
          name="produitPrix"
          placeholder="Prix (DT)"
          min="0"
          step="0.01"
          required
        />
        <i class="fa-solid fa-tag"></i>
      </div>
      <div class="form-group">
        <input
          type="number"
          [(ngModel)]="modalProduit.quantite"
          name="produitQuantite"
          placeholder="Quantité"
          min="0"
          required
        />
        <i class="fa-solid fa-cubes"></i>
      </div>
      <div class="form-group">
        <select [(ngModel)]="modalProduit.categorieId" name="produitCategorie" required>
          <option value="" disabled selected>Sélectionner une catégorie</option>
          <option *ngFor="let cat of categories" [value]="cat.id">{{cat.nom}}</option>
        </select>
        <i class="fa-solid fa-layer-group"></i>
      </div>

      <!-- Zone d'upload ou prévisualisation d'image -->
      <div class="form-group">
        <label for="file-upload" class="file-upload-label" *ngIf="!imageUrlPreview">
          <div class="file-upload">
            <input id="file-upload" type="file" (change)="onFileSelected($event)" accept="image/*" class="file-input" />
            <i class="fa-solid fa-image"></i>
            <span class="file-label">{{ selectedFileName || 'Ajouter ou changer l\'image' }}</span>
          </div>
        </label>

        <div class="image-preview" *ngIf="imageUrlPreview">
          <img [src]="imageUrlPreview" alt="Image prévisualisée" />
          <button class="remove-image-btn" (click)="removeImage()"><i class="fa-solid fa-times"></i></button>
        </div>
      </div>

      <div class="modal-actions">
        <button type="button" class="modal-cancel" (click)="closeModal()">
          <i class="fa-solid fa-times"></i>
          Annuler
        </button>
        <button type="submit" class="modal-validate">
          <i class="fa-solid fa-check"></i>
          {{ isEditMode ? 'Enregistrer' : 'Valider' }}
        </button>
      </div>
    </form>
  </div>

  <!-- Modal de confirmation de suppression -->
  <div class="modal-backdrop" *ngIf="showDeleteModal" (click)="closeDeleteModal()"></div>
  <div class="modal" *ngIf="showDeleteModal">
    <div class="modal-content" (click)="$event.stopPropagation()">
      <h3>Confirmer la suppression</h3>
      <p>Voulez-vous vraiment supprimer le produit <b>{{ produitToDelete?.nom }}</b> ?</p>
      <div class="modal-actions">
        <button type="button" class="modal-cancel" (click)="closeDeleteModal()">
          <i class="fa-solid fa-times"></i>
          Annuler
        </button>
        <button type="button" class="modal-validate delete" (click)="confirmDelete()">
          <i class="fa-solid fa-trash"></i>
          Supprimer
        </button>
      </div>
    </div>
  </div>
</div>
