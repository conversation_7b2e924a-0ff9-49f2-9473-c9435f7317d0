{"clients": [{"nom": "<PERSON><PERSON>", "prenom": "<PERSON>", "email": "<EMAIL>", "telephone": "0123456789", "codeClient": "CLI001", "adresse": "123 Rue de la Paix, Paris"}, {"nom": "<PERSON>", "prenom": "<PERSON>", "email": "<EMAIL>", "telephone": "0987654321", "codeClient": "CLI002", "adresse": "456 Avenue des Champs, Lyon"}, {"nom": "<PERSON>", "prenom": "<PERSON>", "email": "<EMAIL>", "telephone": "0147258369", "codeClient": "CLI003", "adresse": "789 Boulevard Saint-Germain, Marseille"}], "categories": [{"nom": "Électronique", "description": "Produits électroniques et high-tech"}, {"nom": "Vêtements", "description": "Vêtements et accessoires"}], "produits": [{"nom": "Smartphone Samsung", "prix": 599.99, "quantite": 50, "reference": "SAMS001", "categorieId": 1}, {"nom": "Laptop Dell", "prix": 899.99, "quantite": 25, "reference": "DELL001", "categorieId": 1}, {"nom": "T-shirt Nike", "prix": 29.99, "quantite": 0, "reference": "NIKE001", "categorieId": 2}, {"nom": "<PERSON><PERSON>", "prix": 79.99, "quantite": 30, "reference": "LEVI001", "categorieId": 2}], "reglements": [{"factureId": 1, "montantPaye": 1000.0, "modeReglement": "VIREMENT", "reference": "VIR001", "commentaire": "Paiement partiel facture 1"}, {"factureId": 1, "montantPaye": 500.0, "modeReglement": "CHEQUE", "reference": "CHQ001", "commentaire": "Solde facture 1"}, {"factureId": 2, "montantPaye": 200.0, "modeReglement": "ESPECES", "reference": "ESP001", "commentaire": "Acompte facture 2"}]}