/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
export default [[["Mitternacht", "am Morge", "zmittag", "am Namittag", "zaabig", "znacht"], u, u], [["Mitternacht", "am Morge", "zmittag", "am Namittag", "zaabig", "znacht"], u, ["Mitternacht", "Morge", "Mittag", "Namittag", "Aabig", "Nacht"]], ["00:00", ["05:00", "12:00"], ["12:00", "14:00"], ["14:00", "18:00"], ["18:00", "24:00"], ["00:00", "05:00"]]];
//# sourceMappingURL=gsw-FR.js.map