{"name": "vente-plus", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.14", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@cloudinary/angular-5.x": "^1.5.4", "bootstrap-icons": "^1.13.1", "chart.js": "^4.4.0", "chartjs-adapter-date-fns": "^3.0.0", "cloudinary-core": "^2.13.1", "date-fns": "^3.6.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "ng2-charts": "^6.0.1", "ngx-toastr": "^19.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.12", "@angular/cli": "^19.2.12", "@angular/compiler-cli": "^19.2.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}}