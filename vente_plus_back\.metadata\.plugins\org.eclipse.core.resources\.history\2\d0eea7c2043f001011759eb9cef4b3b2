package org.ms.gatewayservice.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;


@Configuration
@EnableWebFluxSecurity
public class SecurityConfig {

    @Bean
    public SecurityWebFilterChain springSecurityFilterChain(ServerHttpSecurity http) {
        return http
            .csrf(ServerHttpSecurity.CsrfSpec::disable)
            .authorizeExchange(exchange -> exchange
                .pathMatchers("/login", "/users/**").permitAll() // routes publiques
                .anyExchange().authenticated() // le reste doit être authentifié
            )
            .oauth2ResourceServer(oauth2 -> oauth2
                .jwt() // ici Spring valide le JWT avec MaClé
            )
            .build();
    }
}
