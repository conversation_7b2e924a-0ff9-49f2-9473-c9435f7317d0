import { Routes } from '@angular/router';
import { LoginComponent } from './components/login/login.component';
import { CategorieComponent } from './components/categorie/categorie.component';
import { LayoutComponent } from './components/layout/layout.component';
import { ProduitComponent } from './components/produit/produit.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { DevisComponent } from './components/devis/devis.component';
import { ClientsComponent } from './components/clients/clients.component';
import { FactureComponent } from './components/facture/facture.component';
import { ReglementComponent } from './components/reglement/reglement.component';
//import { ReglementComponent } from './components/reglement/reglement.component';
export const routes: Routes = [
  // redirection par défaut vers /login
  { path: '', redirectTo: 'login', pathMatch: 'full' },

  // page de login
  { path: 'login', component: LoginComponent },

  // layout protégé
  {
    path: '',
    component: LayoutComponent,
    children: [
      { path: 'categorie', component: CategorieComponent },
      { path: 'produits', component: ProduitComponent },
      { path: 'dashboard', component: DashboardComponent },
      { path: 'devis', component: DevisComponent },
      { path: 'factures', component: FactureComponent },
      { path: 'clients', component: ClientsComponent },
      { path: 'reglements', component: ReglementComponent } 
    ]
  },

  // redirection pour les routes inconnues
  { path: '**', redirectTo: 'login' }
];