# Complete Test Script for Reglement and Dashboard Services
Write-Host "=== Complete Test Suite for Reglement & Dashboard Services ===" -ForegroundColor Green

# Function to make HTTP requests and display results
function Test-Endpoint {
    param(
        [string]$Method,
        [string]$Url,
        [string]$Body = $null,
        [string]$Description
    )
    
    Write-Host "`n--- $Description ---" -ForegroundColor Yellow
    Write-Host "$Method $Url" -ForegroundColor Cyan
    
    try {
        if ($Body) {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Body $Body -ContentType "application/json"
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method
        }
        
        Write-Host "✅ Success:" -ForegroundColor Green
        $response | ConvertTo-Json -Depth 3 | Write-Host
        return $response
    }
    catch {
        Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

Write-Host "`n🔧 Step 1: Creating Test Data..." -ForegroundColor Magenta

# Create Categories first
Write-Host "`n📁 Creating Categories..." -ForegroundColor Blue
Test-Endpoint -Method "POST" -Url "http://localhost:8083/categories" -Description "Create Electronics Category" -Body '{
    "nom": "Électronique",
    "description": "Produits électroniques et high-tech"
}'

Test-Endpoint -Method "POST" -Url "http://localhost:8083/categories" -Description "Create Clothing Category" -Body '{
    "nom": "Vêtements", 
    "description": "Vêtements et accessoires"
}'

# Create Clients
Write-Host "`n👥 Creating Clients..." -ForegroundColor Blue
Test-Endpoint -Method "POST" -Url "http://localhost:8081/clients" -Description "Create Client 1 - Jean Dupont" -Body '{
    "nom": "Dupont",
    "prenom": "Jean",
    "email": "<EMAIL>",
    "telephone": "0123456789",
    "codeClient": "CLI001",
    "adresse": "123 Rue de la Paix, Paris"
}'

Test-Endpoint -Method "POST" -Url "http://localhost:8081/clients" -Description "Create Client 2 - Marie Martin" -Body '{
    "nom": "Martin",
    "prenom": "Marie",
    "email": "<EMAIL>",
    "telephone": "0987654321",
    "codeClient": "CLI002",
    "adresse": "456 Avenue des Champs, Lyon"
}'

Test-Endpoint -Method "POST" -Url "http://localhost:8081/clients" -Description "Create Client 3 - Pierre Bernard" -Body '{
    "nom": "Bernard",
    "prenom": "Pierre",
    "email": "<EMAIL>",
    "telephone": "0147258369",
    "codeClient": "CLI003",
    "adresse": "789 Boulevard Saint-Germain, Marseille"
}'

# Create Products
Write-Host "`n📦 Creating Products..." -ForegroundColor Blue
Test-Endpoint -Method "POST" -Url "http://localhost:8082/produits" -Description "Create Product 1 - Smartphone" -Body '{
    "nom": "Smartphone Samsung",
    "prix": 599.99,
    "quantite": 50,
    "reference": "SAMS001",
    "categorieId": 1
}'

Test-Endpoint -Method "POST" -Url "http://localhost:8082/produits" -Description "Create Product 2 - Laptop" -Body '{
    "nom": "Laptop Dell",
    "prix": 899.99,
    "quantite": 25,
    "reference": "DELL001",
    "categorieId": 1
}'

Test-Endpoint -Method "POST" -Url "http://localhost:8082/produits" -Description "Create Product 3 - T-shirt (Out of Stock)" -Body '{
    "nom": "T-shirt Nike",
    "prix": 29.99,
    "quantite": 0,
    "reference": "NIKE001",
    "categorieId": 2
}'

Test-Endpoint -Method "POST" -Url "http://localhost:8082/produits" -Description "Create Product 4 - Jeans" -Body '{
    "nom": "Jeans Levis",
    "prix": 79.99,
    "quantite": 30,
    "reference": "LEVI001",
    "categorieId": 2
}'

Write-Host "`n⏳ Waiting 3 seconds for data to be created..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# Create Devis (Quotes)
Write-Host "`n📋 Creating Devis (Quotes)..." -ForegroundColor Blue
Test-Endpoint -Method "POST" -Url "http://localhost:8085/devis" -Description "Create Devis 1 for Client 1" -Body '{
    "clientId": 1,
    "dateValidite": "2024-12-31",
    "lignes": [
        {
            "produitId": 1,
            "quantite": 2,
            "prixUnitaire": 599.99,
            "remise": 0,
            "tva": 20
        },
        {
            "produitId": 2,
            "quantite": 1,
            "prixUnitaire": 899.99,
            "remise": 50,
            "tva": 20
        }
    ]
}'

Test-Endpoint -Method "POST" -Url "http://localhost:8085/devis" -Description "Create Devis 2 for Client 2" -Body '{
    "clientId": 2,
    "dateValidite": "2024-12-31",
    "lignes": [
        {
            "produitId": 3,
            "quantite": 3,
            "prixUnitaire": 29.99,
            "remise": 0,
            "tva": 20
        },
        {
            "produitId": 4,
            "quantite": 2,
            "prixUnitaire": 79.99,
            "remise": 10,
            "tva": 20
        }
    ]
}'

Test-Endpoint -Method "POST" -Url "http://localhost:8085/devis" -Description "Create Devis 3 for Client 3" -Body '{
    "clientId": 3,
    "dateValidite": "2024-12-31",
    "lignes": [
        {
            "produitId": 1,
            "quantite": 1,
            "prixUnitaire": 599.99,
            "remise": 0,
            "tva": 20
        }
    ]
}'

Write-Host "`n⏳ Waiting 2 seconds..." -ForegroundColor Yellow
Start-Sleep -Seconds 2

# Validate Devis
Write-Host "`n✅ Validating Devis..." -ForegroundColor Blue
Test-Endpoint -Method "PUT" -Url "http://localhost:8085/devis/1/valider" -Description "Validate Devis 1"
Test-Endpoint -Method "PUT" -Url "http://localhost:8085/devis/2/valider" -Description "Validate Devis 2"
Test-Endpoint -Method "PUT" -Url "http://localhost:8085/devis/3/valider" -Description "Validate Devis 3"

Write-Host "`n⏳ Waiting 2 seconds..." -ForegroundColor Yellow
Start-Sleep -Seconds 2

# Create Invoices from Devis
Write-Host "`n🧾 Creating Invoices from Devis..." -ForegroundColor Blue
Test-Endpoint -Method "POST" -Url "http://localhost:8084/factures/from-devis/1" -Description "Create Invoice from Devis 1"
Test-Endpoint -Method "POST" -Url "http://localhost:8084/factures/from-devis/2" -Description "Create Invoice from Devis 2"
Test-Endpoint -Method "POST" -Url "http://localhost:8084/factures/from-devis/3" -Description "Create Invoice from Devis 3"

Write-Host "`n⏳ Waiting 3 seconds for invoices to be created..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

Write-Host "`n💰 Step 2: Testing Reglement Service..." -ForegroundColor Magenta

# Create Payments
Write-Host "`n💳 Creating Payments..." -ForegroundColor Blue
Test-Endpoint -Method "POST" -Url "http://localhost:8088/reglements" -Description "Full Payment for Invoice 1" -Body '{
    "factureId": 1,
    "montantPaye": 2099.97,
    "modeReglement": "VIREMENT",
    "reference": "VIR001",
    "commentaire": "Paiement complet facture 1"
}'

Test-Endpoint -Method "POST" -Url "http://localhost:8088/reglements" -Description "Partial Payment for Invoice 2" -Body '{
    "factureId": 2,
    "montantPaye": 100.00,
    "modeReglement": "CHEQUE",
    "reference": "CHQ001",
    "commentaire": "Acompte facture 2"
}'

# Test Reglement Service Endpoints
Write-Host "`n🧪 Testing Reglement Service Endpoints..." -ForegroundColor Blue
Test-Endpoint -Method "GET" -Url "http://localhost:8088/reglements" -Description "Get All Payments"
Test-Endpoint -Method "GET" -Url "http://localhost:8088/reglements/client/1" -Description "Get Payments by Client 1"
Test-Endpoint -Method "GET" -Url "http://localhost:8088/reglements/total/client/1" -Description "Total Payments by Client 1"
Test-Endpoint -Method "GET" -Url "http://localhost:8088/reglements/factures/non-reglees" -Description "Unpaid Invoices"
Test-Endpoint -Method "GET" -Url "http://localhost:8088/reglements/factures/partiellement-reglees" -Description "Partially Paid Invoices"

Write-Host "`n📊 Step 3: Testing Dashboard Service..." -ForegroundColor Magenta

# Test Dashboard Analytics
Write-Host "`n📈 Testing Client Analytics..." -ForegroundColor Blue
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/client/1/analytics" -Description "Client 1 Complete Analytics"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/client/2/analytics" -Description "Client 2 Complete Analytics"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/clients/analytics" -Description "All Clients Analytics"

Write-Host "`n🏆 Testing Loyalty Analytics..." -ForegroundColor Blue
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/clients/fideles?limit=5" -Description "Most Loyal Clients"

Write-Host "`n📦 Testing Product Analytics..." -ForegroundColor Blue
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/produits/plus-vendus?limit=10" -Description "Best Selling Products"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/produits/plus-vendus/annee/2024?limit=10" -Description "Best Selling Products 2024"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/produits/rupture-stock" -Description "Out of Stock Products"

Write-Host "`n🧾 Testing Invoice Status..." -ForegroundColor Blue
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/factures/reglees" -Description "Paid Invoices"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/factures/non-reglees" -Description "Unpaid Invoices"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/factures/partiellement-reglees" -Description "Partially Paid Invoices"

Write-Host "`n💸 Testing Debt Management..." -ForegroundColor Blue
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/dettes" -Description "All Client Debts"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/client/1/dettes" -Description "Client 1 Debt"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/client/2/dettes" -Description "Client 2 Debt"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/client/3/dettes" -Description "Client 3 Debt"

Write-Host "`n💰 Testing Revenue Analytics..." -ForegroundColor Blue
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/client/1/chiffres-affaires" -Description "Client 1 Total Revenue"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/client/2/chiffres-affaires" -Description "Client 2 Total Revenue"
Test-Endpoint -Method "GET" -Url "http://localhost:8089/dashboard/client/1/chiffres-affaires/annee/2024" -Description "Client 1 Revenue 2024"

Write-Host "`n🎉 Testing Complete!" -ForegroundColor Green
Write-Host "All endpoints have been tested. Check the results above!" -ForegroundColor White
Write-Host "`nExpected Results Summary:" -ForegroundColor Cyan
Write-Host "- 3 clients created" -ForegroundColor White
Write-Host "- 4 products created - 1 out of stock" -ForegroundColor White
Write-Host "- 3 quotes created and validated" -ForegroundColor White
Write-Host "- 3 invoices created" -ForegroundColor White
Write-Host "- 2 payments created - 1 full and 1 partial" -ForegroundColor White
Write-Host "- All dashboard analytics working" -ForegroundColor White
