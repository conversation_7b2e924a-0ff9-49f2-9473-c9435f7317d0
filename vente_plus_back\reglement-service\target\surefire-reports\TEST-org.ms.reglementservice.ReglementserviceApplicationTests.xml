<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="org.ms.reglementservice.ReglementserviceApplicationTests" time="10.508" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="C:\project_microservice\ventePlus-app\vente_plus_back\reglement-service\target\test-classes;C:\project_microservice\ventePlus-app\vente_plus_back\reglement-service\target\classes;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-actuator\3.1.3\spring-boot-starter-actuator-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter\3.1.3\spring-boot-starter-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-logging\3.1.3\spring-boot-starter-logging-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\logging\log4j\log4j-to-slf4j\2.20.0\log4j-to-slf4j-2.20.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\logging\log4j\log4j-api\2.20.0\log4j-api-2.20.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\slf4j\jul-to-slf4j\2.0.7\jul-to-slf4j-2.0.7.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\yaml\snakeyaml\1.33\snakeyaml-1.33.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-actuator-autoconfigure\3.1.3\spring-boot-actuator-autoconfigure-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-actuator\3.1.3\spring-boot-actuator-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.2\jackson-datatype-jsr310-2.15.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\micrometer\micrometer-observation\1.11.3\micrometer-observation-1.11.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\micrometer\micrometer-commons\1.11.3\micrometer-commons-1.11.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\micrometer\micrometer-core\1.11.3\micrometer-core-1.11.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-data-jpa\3.1.3\spring-boot-starter-data-jpa-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-aop\3.1.3\spring-boot-starter-aop-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-aop\6.0.11\spring-aop-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\aspectj\aspectjweaver\1.9.20\aspectjweaver-1.9.20.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-jdbc\3.1.3\spring-boot-starter-jdbc-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-jdbc\6.0.11\spring-jdbc-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\hibernate\orm\hibernate-core\6.2.7.Final\hibernate-core-6.2.7.Final.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\hibernate\common\hibernate-commons-annotations\6.0.6.Final\hibernate-commons-annotations-6.0.6.Final.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\smallrye\jandex\3.0.5\jandex-3.0.5.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\net\bytebuddy\byte-buddy\1.14.6\byte-buddy-1.14.6.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\glassfish\jaxb\jaxb-runtime\4.0.3\jaxb-runtime-4.0.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\glassfish\jaxb\jaxb-core\4.0.3\jaxb-core-4.0.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\eclipse\angus\angus-activation\2.0.1\angus-activation-2.0.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\glassfish\jaxb\txw2\4.0.3\txw2-4.0.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\antlr\antlr4-runtime\4.10.1\antlr4-runtime-4.10.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\data\spring-data-jpa\3.1.3\spring-data-jpa-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\data\spring-data-commons\3.1.3\spring-data-commons-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-orm\6.0.11\spring-orm-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-context\6.0.11\spring-context-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-tx\6.0.11\spring-tx-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-beans\6.0.11\spring-beans-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-aspects\6.0.11\spring-aspects-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-data-rest\3.1.3\spring-boot-starter-data-rest-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\data\spring-data-rest-webmvc\4.1.3\spring-data-rest-webmvc-4.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\data\spring-data-rest-core\4.1.3\spring-data-rest-core-4.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\hateoas\spring-hateoas\2.1.2\spring-hateoas-2.1.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\plugin\spring-plugin-core\3.0.0\spring-plugin-core-3.0.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\atteo\evo-inflector\1.3\evo-inflector-1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-web\3.1.3\spring-boot-starter-web-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-json\3.1.3\spring-boot-starter-json-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.2\jackson-datatype-jdk8-2.15.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.2\jackson-module-parameter-names-2.15.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-tomcat\3.1.3\spring-boot-starter-tomcat-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\tomcat\embed\tomcat-embed-core\10.1.12\tomcat-embed-core-10.1.12.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\tomcat\embed\tomcat-embed-el\10.1.12\tomcat-embed-el-10.1.12.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.12\tomcat-embed-websocket-10.1.12.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-web\6.0.11\spring-web-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-webmvc\6.0.11\spring-webmvc-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-expression\6.0.11\spring-expression-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-starter-netflix-eureka-client\4.0.3\spring-cloud-starter-netflix-eureka-client-4.0.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-starter\4.0.4\spring-cloud-starter-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-context\4.0.4\spring-cloud-context-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\security\spring-security-rsa\1.0.12.RELEASE\spring-security-rsa-1.0.12.RELEASE.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\bouncycastle\bcpkix-jdk18on\1.73\bcpkix-jdk18on-1.73.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\bouncycastle\bcprov-jdk18on\1.73\bcprov-jdk18on-1.73.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\bouncycastle\bcutil-jdk18on\1.73\bcutil-jdk18on-1.73.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-netflix-eureka-client\4.0.3\spring-cloud-netflix-eureka-client-4.0.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\httpcomponents\client5\httpclient5\5.2.1\httpclient5-5.2.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\httpcomponents\core5\httpcore5\5.2.2\httpcore5-5.2.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\httpcomponents\core5\httpcore5-h2\5.2.2\httpcore5-h2-5.2.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\netflix\eureka\eureka-client\2.0.1\eureka-client-2.0.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\netflix\netflix-commons\netflix-eventbus\0.3.0\netflix-eventbus-0.3.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\netflix\netflix-commons\netflix-infix\0.3.0\netflix-infix-0.3.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\commons-jxpath\commons-jxpath\1.3\commons-jxpath-1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\joda-time\joda-time\2.3\joda-time-2.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\antlr\antlr-runtime\3.4\antlr-runtime-3.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\antlr\stringtemplate\3.2.1\stringtemplate-3.2.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\commons\commons-math\2.2\commons-math-2.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\thoughtworks\xstream\xstream\1.4.19\xstream-1.4.19.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\ws\rs\jakarta.ws.rs-api\3.1.0\jakarta.ws.rs-api-3.1.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\google\guava\guava\19.0\guava-19.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\commons-configuration\commons-configuration\1.10\commons-configuration-1.10.jar;C:\dsir\Dev\Outils\Maven\depot_local\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\codehaus\jettison\jettison\1.4.0\jettison-1.4.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\netflix\eureka\eureka-core\2.0.1\eureka-core-2.0.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\servlet\jakarta.servlet-api\6.0.0\jakarta.servlet-api-6.0.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\woodstox\woodstox-core\6.2.1\woodstox-core-6.2.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\codehaus\woodstox\stax2-api\4.2.1\stax2-api-4.2.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-starter-loadbalancer\4.0.4\spring-cloud-starter-loadbalancer-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-loadbalancer\4.0.4\spring-cloud-loadbalancer-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\projectreactor\reactor-core\3.5.9\reactor-core-3.5.9.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\projectreactor\addons\reactor-extra\3.5.1\reactor-extra-3.5.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-cache\3.1.3\spring-boot-starter-cache-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-context-support\6.0.11\spring-context-support-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-starter-config\4.0.4\spring-cloud-starter-config-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-config-client\4.0.4\spring-cloud-config-client-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-devtools\3.1.3\spring-boot-devtools-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot\3.1.3\spring-boot-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-autoconfigure\3.1.3\spring-boot-autoconfigure-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\postgresql\postgresql\42.6.0\postgresql-42.6.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\checkerframework\checker-qual\3.31.0\checker-qual-3.31.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\projectlombok\lombok\1.18.28\lombok-1.18.28.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-test\3.1.3\spring-boot-starter-test-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-test\3.1.3\spring-boot-test-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-test-autoconfigure\3.1.3\spring-boot-test-autoconfigure-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\xml\bind\jakarta.xml.bind-api\4.0.0\jakarta.xml.bind-api-4.0.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\ow2\asm\asm\9.3\asm-9.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\junit\jupiter\junit-jupiter\5.9.3\junit-jupiter-5.9.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\junit\jupiter\junit-jupiter-api\5.9.3\junit-jupiter-api-5.9.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\junit\platform\junit-platform-commons\1.9.3\junit-platform-commons-1.9.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\junit\jupiter\junit-jupiter-params\5.9.3\junit-jupiter-params-5.9.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\junit\jupiter\junit-jupiter-engine\5.9.3\junit-jupiter-engine-5.9.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\junit\platform\junit-platform-engine\1.9.3\junit-platform-engine-1.9.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\mockito\mockito-core\5.3.1\mockito-core-5.3.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\net\bytebuddy\byte-buddy-agent\1.14.6\byte-buddy-agent-1.14.6.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\mockito\mockito-junit-jupiter\5.3.1\mockito-junit-jupiter-5.3.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-core\6.0.11\spring-core-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-jcl\6.0.11\spring-jcl-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-test\6.0.11\spring-test-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\data\spring-data-rest-hal-browser\3.3.9.RELEASE\spring-data-rest-hal-browser-3.3.9.RELEASE.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-starter-bootstrap\4.0.4\spring-cloud-starter-bootstrap-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-starter-openfeign\4.0.4\spring-cloud-starter-openfeign-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-openfeign-core\4.0.4\spring-cloud-openfeign-core-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;C:\dsir\Dev\Outils\Maven\depot_local\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-commons\4.0.4\spring-cloud-commons-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\security\spring-security-crypto\6.1.3\spring-security-crypto-6.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\github\openfeign\feign-core\12.4\feign-core-12.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\github\openfeign\feign-slf4j\12.4\feign-slf4j-12.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\cloudinary\cloudinary-http44\1.37.0\cloudinary-http44-1.37.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\cloudinary\cloudinary-core\1.37.0\cloudinary-core-1.37.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\httpcomponents\httpclient\4.4\httpclient-4.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\dsir\Dev\Outils\Maven\depot_local\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\httpcomponents\httpmime\4.4\httpmime-4.4.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Africa/Tunis"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="FR"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-17\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire1262017815199565427\surefirebooter-20250607140820449_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire1262017815199565427 2025-06-07T14-08-20_262-jvmRun1 surefire-20250607140820449_1tmp surefire_0-20250607140820449_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="C:\project_microservice\ventePlus-app\vente_plus_back\reglement-service\target\test-classes;C:\project_microservice\ventePlus-app\vente_plus_back\reglement-service\target\classes;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-actuator\3.1.3\spring-boot-starter-actuator-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter\3.1.3\spring-boot-starter-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-logging\3.1.3\spring-boot-starter-logging-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\logging\log4j\log4j-to-slf4j\2.20.0\log4j-to-slf4j-2.20.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\logging\log4j\log4j-api\2.20.0\log4j-api-2.20.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\slf4j\jul-to-slf4j\2.0.7\jul-to-slf4j-2.0.7.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\yaml\snakeyaml\1.33\snakeyaml-1.33.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-actuator-autoconfigure\3.1.3\spring-boot-actuator-autoconfigure-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-actuator\3.1.3\spring-boot-actuator-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.2\jackson-datatype-jsr310-2.15.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\micrometer\micrometer-observation\1.11.3\micrometer-observation-1.11.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\micrometer\micrometer-commons\1.11.3\micrometer-commons-1.11.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\micrometer\micrometer-core\1.11.3\micrometer-core-1.11.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-data-jpa\3.1.3\spring-boot-starter-data-jpa-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-aop\3.1.3\spring-boot-starter-aop-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-aop\6.0.11\spring-aop-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\aspectj\aspectjweaver\1.9.20\aspectjweaver-1.9.20.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-jdbc\3.1.3\spring-boot-starter-jdbc-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-jdbc\6.0.11\spring-jdbc-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\hibernate\orm\hibernate-core\6.2.7.Final\hibernate-core-6.2.7.Final.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\hibernate\common\hibernate-commons-annotations\6.0.6.Final\hibernate-commons-annotations-6.0.6.Final.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\smallrye\jandex\3.0.5\jandex-3.0.5.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\net\bytebuddy\byte-buddy\1.14.6\byte-buddy-1.14.6.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\glassfish\jaxb\jaxb-runtime\4.0.3\jaxb-runtime-4.0.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\glassfish\jaxb\jaxb-core\4.0.3\jaxb-core-4.0.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\eclipse\angus\angus-activation\2.0.1\angus-activation-2.0.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\glassfish\jaxb\txw2\4.0.3\txw2-4.0.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\antlr\antlr4-runtime\4.10.1\antlr4-runtime-4.10.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\data\spring-data-jpa\3.1.3\spring-data-jpa-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\data\spring-data-commons\3.1.3\spring-data-commons-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-orm\6.0.11\spring-orm-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-context\6.0.11\spring-context-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-tx\6.0.11\spring-tx-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-beans\6.0.11\spring-beans-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-aspects\6.0.11\spring-aspects-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-data-rest\3.1.3\spring-boot-starter-data-rest-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\data\spring-data-rest-webmvc\4.1.3\spring-data-rest-webmvc-4.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\data\spring-data-rest-core\4.1.3\spring-data-rest-core-4.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\hateoas\spring-hateoas\2.1.2\spring-hateoas-2.1.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\plugin\spring-plugin-core\3.0.0\spring-plugin-core-3.0.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\atteo\evo-inflector\1.3\evo-inflector-1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-web\3.1.3\spring-boot-starter-web-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-json\3.1.3\spring-boot-starter-json-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.2\jackson-datatype-jdk8-2.15.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.2\jackson-module-parameter-names-2.15.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-tomcat\3.1.3\spring-boot-starter-tomcat-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\tomcat\embed\tomcat-embed-core\10.1.12\tomcat-embed-core-10.1.12.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\tomcat\embed\tomcat-embed-el\10.1.12\tomcat-embed-el-10.1.12.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.12\tomcat-embed-websocket-10.1.12.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-web\6.0.11\spring-web-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-webmvc\6.0.11\spring-webmvc-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-expression\6.0.11\spring-expression-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-starter-netflix-eureka-client\4.0.3\spring-cloud-starter-netflix-eureka-client-4.0.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-starter\4.0.4\spring-cloud-starter-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-context\4.0.4\spring-cloud-context-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\security\spring-security-rsa\1.0.12.RELEASE\spring-security-rsa-1.0.12.RELEASE.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\bouncycastle\bcpkix-jdk18on\1.73\bcpkix-jdk18on-1.73.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\bouncycastle\bcprov-jdk18on\1.73\bcprov-jdk18on-1.73.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\bouncycastle\bcutil-jdk18on\1.73\bcutil-jdk18on-1.73.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-netflix-eureka-client\4.0.3\spring-cloud-netflix-eureka-client-4.0.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\httpcomponents\client5\httpclient5\5.2.1\httpclient5-5.2.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\httpcomponents\core5\httpcore5\5.2.2\httpcore5-5.2.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\httpcomponents\core5\httpcore5-h2\5.2.2\httpcore5-h2-5.2.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\netflix\eureka\eureka-client\2.0.1\eureka-client-2.0.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\netflix\netflix-commons\netflix-eventbus\0.3.0\netflix-eventbus-0.3.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\netflix\netflix-commons\netflix-infix\0.3.0\netflix-infix-0.3.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\commons-jxpath\commons-jxpath\1.3\commons-jxpath-1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\joda-time\joda-time\2.3\joda-time-2.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\antlr\antlr-runtime\3.4\antlr-runtime-3.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\antlr\stringtemplate\3.2.1\stringtemplate-3.2.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\commons\commons-math\2.2\commons-math-2.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\thoughtworks\xstream\xstream\1.4.19\xstream-1.4.19.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\ws\rs\jakarta.ws.rs-api\3.1.0\jakarta.ws.rs-api-3.1.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\google\guava\guava\19.0\guava-19.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\commons-configuration\commons-configuration\1.10\commons-configuration-1.10.jar;C:\dsir\Dev\Outils\Maven\depot_local\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\codehaus\jettison\jettison\1.4.0\jettison-1.4.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\netflix\eureka\eureka-core\2.0.1\eureka-core-2.0.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\servlet\jakarta.servlet-api\6.0.0\jakarta.servlet-api-6.0.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\woodstox\woodstox-core\6.2.1\woodstox-core-6.2.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\codehaus\woodstox\stax2-api\4.2.1\stax2-api-4.2.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-starter-loadbalancer\4.0.4\spring-cloud-starter-loadbalancer-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-loadbalancer\4.0.4\spring-cloud-loadbalancer-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\projectreactor\reactor-core\3.5.9\reactor-core-3.5.9.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\projectreactor\addons\reactor-extra\3.5.1\reactor-extra-3.5.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-cache\3.1.3\spring-boot-starter-cache-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-context-support\6.0.11\spring-context-support-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-starter-config\4.0.4\spring-cloud-starter-config-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-config-client\4.0.4\spring-cloud-config-client-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-devtools\3.1.3\spring-boot-devtools-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot\3.1.3\spring-boot-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-autoconfigure\3.1.3\spring-boot-autoconfigure-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\postgresql\postgresql\42.6.0\postgresql-42.6.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\checkerframework\checker-qual\3.31.0\checker-qual-3.31.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\projectlombok\lombok\1.18.28\lombok-1.18.28.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-starter-test\3.1.3\spring-boot-starter-test-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-test\3.1.3\spring-boot-test-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\boot\spring-boot-test-autoconfigure\3.1.3\spring-boot-test-autoconfigure-3.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\xml\bind\jakarta.xml.bind-api\4.0.0\jakarta.xml.bind-api-4.0.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\ow2\asm\asm\9.3\asm-9.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\junit\jupiter\junit-jupiter\5.9.3\junit-jupiter-5.9.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\junit\jupiter\junit-jupiter-api\5.9.3\junit-jupiter-api-5.9.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\junit\platform\junit-platform-commons\1.9.3\junit-platform-commons-1.9.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\junit\jupiter\junit-jupiter-params\5.9.3\junit-jupiter-params-5.9.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\junit\jupiter\junit-jupiter-engine\5.9.3\junit-jupiter-engine-5.9.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\junit\platform\junit-platform-engine\1.9.3\junit-platform-engine-1.9.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\mockito\mockito-core\5.3.1\mockito-core-5.3.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\net\bytebuddy\byte-buddy-agent\1.14.6\byte-buddy-agent-1.14.6.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\mockito\mockito-junit-jupiter\5.3.1\mockito-junit-jupiter-5.3.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-core\6.0.11\spring-core-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-jcl\6.0.11\spring-jcl-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\spring-test\6.0.11\spring-test-6.0.11.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\data\spring-data-rest-hal-browser\3.3.9.RELEASE\spring-data-rest-hal-browser-3.3.9.RELEASE.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-starter-bootstrap\4.0.4\spring-cloud-starter-bootstrap-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-starter-openfeign\4.0.4\spring-cloud-starter-openfeign-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-openfeign-core\4.0.4\spring-cloud-openfeign-core-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;C:\dsir\Dev\Outils\Maven\depot_local\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\cloud\spring-cloud-commons\4.0.4\spring-cloud-commons-4.0.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\springframework\security\spring-security-crypto\6.1.3\spring-security-crypto-6.1.3.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\github\openfeign\feign-core\12.4\feign-core-12.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\io\github\openfeign\feign-slf4j\12.4\feign-slf4j-12.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\cloudinary\cloudinary-http44\1.37.0\cloudinary-http44-1.37.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\com\cloudinary\cloudinary-core\1.37.0\cloudinary-core-1.37.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\httpcomponents\httpclient\4.4\httpclient-4.4.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\dsir\Dev\Outils\Maven\depot_local\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\dsir\Dev\Outils\Maven\depot_local\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\dsir\Dev\Outils\Maven\depot_local\org\apache\httpcomponents\httpmime\4.4\httpmime-4.4.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-17"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\project_microservice\ventePlus-app\vente_plus_back\reglement-service"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="windows-1252"/>
    <property name="java.awt.headless" value="true"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire1262017815199565427\surefirebooter-20250607140820449_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.6+9-LTS-190"/>
    <property name="user.name" value="Asus TUF"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="Cp1252"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\dsir\Dev\Outils\Maven\depot_local"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="com.zaxxer.hikari.pool_number" value="1"/>
    <property name="java.version" value="17.0.6"/>
    <property name="user.dir" value="C:\project_microservice\ventePlus-app\vente_plus_back\reglement-service"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="15680"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="CONSOLE_LOG_CHARSET" value="windows-1252"/>
    <property name="native.encoding" value="Cp1252"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Users\<USER>\AppData\Local\Pub\Cache\bin;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\sqlite\sqlite-tools-win32-x86-3420000;C:\Program Files\Java\jdk-17\bin;C:\oraclexe\app\oracle\product\11.2.0\server\bin;C:\Windows\System32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\nodejs\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;C:\devmobile\flutter\bin;C:\dsir\Dev\Outils\Maven\apache-maven-3.9.9\bin;C:\Program Files\PuTTY\;C:\Program Files (x86)\Vagrant\bin;C:\Users\<USER>\Downloads\ffmpeg\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Docker\Docker\resources\bin;C:\hadoop-2.8.0\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\.dotnet\tools;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2023.1\bin;;C:\Program Files\JetBrains\IntelliJ IDEA 2023.2.5\bin;C:\Program Files\Java\jdk1.8.0_05\bin;;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.6+9-LTS-190"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="contextLoads" classname="org.ms.reglementservice.ReglementserviceApplicationTests" time="0.56">
    <system-out><![CDATA[14:08:21.182 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils -- Could not detect default configuration classes for test class [org.ms.reglementservice.ReglementserviceApplicationTests]: ReglementserviceApplicationTests does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
14:08:21.277 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper -- Found @SpringBootConfiguration org.ms.reglementservice.ReglementserviceApplication for test class org.ms.reglementservice.ReglementserviceApplicationTests

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.1.3)

2025-06-07T14:08:24.302+01:00  INFO 15680 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Fetching config from server at : http://localhost:5555
2025-06-07T14:08:24.478+01:00  INFO 15680 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Located environment: name=produitservice, profiles=[default], label=null, version=87268d8e30e66268e98706093ec48577263d14d2, state=null
2025-06-07T14:08:24.480+01:00  INFO 15680 --- [           main] b.c.PropertySourceBootstrapConfiguration : Located property source: [BootstrapPropertySource {name='bootstrapProperties-configClient'}, BootstrapPropertySource {name='bootstrapProperties-file://C:\Users\<USER>\Users\Asus TUF/cloud-conf/file:/C:/Users/<USER>/cloud-conf/application.properties'}]
2025-06-07T14:08:24.503+01:00  INFO 15680 --- [           main] o.m.r.ReglementserviceApplicationTests   : No active profile set, falling back to 1 default profile: "default"
2025-06-07T14:08:25.200+01:00  INFO 15680 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-07T14:08:25.214+01:00  INFO 15680 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 0 JPA repository interfaces.
2025-06-07T14:08:25.532+01:00  INFO 15680 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=994d91ef-38ae-32dd-96b6-9302cfa8e963
2025-06-07T14:08:25.870+01:00  INFO 15680 --- [           main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-07T14:08:25.928+01:00  INFO 15680 --- [           main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.2.7.Final
2025-06-07T14:08:25.931+01:00  INFO 15680 --- [           main] org.hibernate.cfg.Environment            : HHH000406: Using bytecode reflection optimizer
2025-06-07T14:08:26.119+01:00  INFO 15680 --- [           main] o.h.b.i.BytecodeProviderInitiator        : HHH000021: Bytecode provider name : bytebuddy
2025-06-07T14:08:26.249+01:00  INFO 15680 --- [           main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-07T14:08:26.265+01:00  INFO 15680 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-07T14:08:26.562+01:00  INFO 15680 --- [           main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@14ad42
2025-06-07T14:08:26.564+01:00  INFO 15680 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-07T14:08:26.678+01:00  INFO 15680 --- [           main] o.h.b.i.BytecodeProviderInitiator        : HHH000021: Bytecode provider name : bytebuddy
2025-06-07T14:08:26.853+01:00  INFO 15680 --- [           main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-07T14:08:26.856+01:00  INFO 15680 --- [           main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-07T14:08:27.024+01:00  WARN 15680 --- [           main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-07T14:08:28.816+01:00  INFO 15680 --- [           main] o.s.cloud.commons.util.InetUtils         : Cannot determine local hostname
2025-06-07T14:08:30.419+01:00  INFO 15680 --- [           main] o.s.cloud.commons.util.InetUtils         : Cannot determine local hostname
2025-06-07T14:08:30.427+01:00  INFO 15680 --- [           main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-07T14:08:30.470+01:00  WARN 15680 --- [           main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-07T14:08:30.498+01:00  INFO 15680 --- [           main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-07T14:08:30.566+01:00  INFO 15680 --- [           main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-07T14:08:30.634+01:00  INFO 15680 --- [           main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-07T14:08:30.641+01:00  INFO 15680 --- [           main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-07T14:08:30.664+01:00  INFO 15680 --- [           main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-07T14:08:30.664+01:00  INFO 15680 --- [           main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-07T14:08:30.664+01:00  INFO 15680 --- [           main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-07T14:08:30.664+01:00  INFO 15680 --- [           main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-07T14:08:30.664+01:00  INFO 15680 --- [           main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-07T14:08:30.664+01:00  INFO 15680 --- [           main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-07T14:08:30.664+01:00  INFO 15680 --- [           main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-07T14:08:30.929+01:00  INFO 15680 --- [           main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-07T14:08:30.931+01:00  INFO 15680 --- [           main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-06-07T14:08:30.934+01:00  INFO 15680 --- [           main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-07T14:08:30.938+01:00  INFO 15680 --- [           main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1749301710937 with initial instances count: 0
2025-06-07T14:08:30.941+01:00  INFO 15680 --- [           main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application PRODUITSERVICE with eureka with status UP
2025-06-07T14:08:30.942+01:00  INFO 15680 --- [           main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1749301710942, current=UP, previous=STARTING]
2025-06-07T14:08:30.945+01:00  INFO 15680 --- [nfoReplicator-0] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_PRODUITSERVICE/localhost:produitservice:8082: registering service...
2025-06-07T14:08:30.960+01:00  INFO 15680 --- [           main] o.m.r.ReglementserviceApplicationTests   : Started ReglementserviceApplicationTests in 9.526 seconds (process running for 10.422)
2025-06-07T14:08:30.986+01:00  INFO 15680 --- [nfoReplicator-0] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_PRODUITSERVICE/localhost:produitservice:8082 - registration status: 204
]]></system-out>
  </testcase>
</testsuite>