# Testing Guide for Reglement & Dashboard Services

## Prerequisites
Make sure all services are running:
- ✅ Eureka Discovery Service (port 8761)
- ✅ Config Service (port 5555)
- ✅ Client Service (port 8081)
- ✅ Produit Service (port 8082)
- ✅ Categorie Service (port 8083)
- ✅ Facture Service (port 8084)
- ✅ Devis Service (port 8085)
- ✅ Reglement Service (port 8088)
- ✅ Dashboard Service (port 8089)

## How to Run Tests

### Option 1: PowerShell Script (Windows)
```powershell
cd vente_plus_back
.\test-complete.ps1
```

### Option 2: <PERSON>sh Script (Linux/Mac)
```bash
cd vente_plus_back
./test-complete.sh
```

### Option 3: Manual Testing with curl

#### Step 1: Create Test Data
```bash
# Create Categories
curl -X POST http://localhost:8083/categories -H "Content-Type: application/json" -d '{"nom": "Électronique", "description": "Produits électroniques"}'

# Create Clients
curl -X POST http://localhost:8081/clients -H "Content-Type: application/json" -d '{"nom": "<PERSON>pont", "prenom": "<PERSON>", "email": "<EMAIL>", "telephone": "0123456789", "codeClient": "CLI001", "adresse": "123 Rue de la Paix, Paris"}'

# Create Products
curl -X POST http://localhost:8082/produits -H "Content-Type: application/json" -d '{"nom": "Smartphone Samsung", "prix": 599.99, "quantite": 50, "reference": "SAMS001", "categorieId": 1}'
```

#### Step 2: Test Reglement Service
```bash
# Get all payments
curl -X GET http://localhost:8088/reglements

# Create a payment
curl -X POST http://localhost:8088/reglements -H "Content-Type: application/json" -d '{"factureId": 1, "montantPaye": 500.00, "modeReglement": "VIREMENT", "reference": "TEST001", "commentaire": "Test payment"}'

# Get payments by client
curl -X GET http://localhost:8088/reglements/client/1

# Get unpaid invoices
curl -X GET http://localhost:8088/reglements/factures/non-reglees
```

#### Step 3: Test Dashboard Service
```bash
# Get client analytics
curl -X GET http://localhost:8089/dashboard/client/1/analytics

# Get most loyal clients
curl -X GET http://localhost:8089/dashboard/clients/fideles?limit=5

# Get best-selling products
curl -X GET http://localhost:8089/dashboard/produits/plus-vendus?limit=10

# Get out-of-stock products
curl -X GET http://localhost:8089/dashboard/produits/rupture-stock

# Get client debts
curl -X GET http://localhost:8089/dashboard/dettes
```

## Expected Test Results

### After Running Complete Test:
- **3 clients** created (Jean Dupont, Marie Martin, Pierre Bernard)
- **4 products** created (1 out of stock - T-shirt Nike)
- **3 quotes** created and validated
- **3 invoices** created from quotes
- **2 payments** created (1 full payment, 1 partial payment)

### Dashboard Analytics Should Show:
- **Client 1 (Jean)**: Fully paid invoice, high revenue
- **Client 2 (Marie)**: Partially paid invoice, some debt
- **Client 3 (Pierre)**: Unpaid invoice, full debt
- **Out of stock**: T-shirt Nike (quantity = 0)
- **Best selling**: Smartphone Samsung (most popular)

## Troubleshooting

### If Services Don't Respond:
1. Check if all services are running
2. Verify Eureka dashboard at http://localhost:8761
3. Check service logs for errors

### If Database Errors:
1. Ensure PostgreSQL is running
2. Check database connections in config files
3. Verify database schemas exist

### If Feign Client Errors:
1. Check service discovery in Eureka
2. Verify service names match in Feign clients
3. Check network connectivity between services

## Quick Health Check Commands

```bash
# Check if services are registered in Eureka
curl -X GET http://localhost:8761/eureka/apps

# Check individual service health
curl -X GET http://localhost:8088/actuator/health  # Reglement Service
curl -X GET http://localhost:8089/actuator/health  # Dashboard Service

# Check if services can communicate
curl -X GET http://localhost:8089/dashboard/clients/analytics
```

## Test Data Summary

The test creates a realistic business scenario:
- **Electronics company** selling smartphones and laptops
- **Clothing company** selling t-shirts and jeans
- **3 customers** with different payment behaviors
- **Mixed payment status** (paid, partial, unpaid)
- **Stock management** (some products out of stock)

This allows testing all dashboard features:
- Revenue analytics per client
- Payment tracking
- Debt management
- Product popularity
- Stock alerts
- Loyalty analysis
