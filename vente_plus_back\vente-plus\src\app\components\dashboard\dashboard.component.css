/* Dashboard Styles */
.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 10px;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Card Styles */
.card {
  border: none;
  border-radius: 10px;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
  background: transparent;
  border-bottom: 1px solid #e3e6f0;
  border-radius: 10px 10px 0 0 !important;
}

/* Border Left Colors */
.border-left-primary {
  border-left: 4px solid #4e73df !important;
}

.border-left-success {
  border-left: 4px solid #1cc88a !important;
}

.border-left-info {
  border-left: 4px solid #36b9cc !important;
}

.border-left-warning {
  border-left: 4px solid #f6c23e !important;
}

.border-left-danger {
  border-left: 4px solid #e74a3b !important;
}

/* Chart Container */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

.chart-container canvas {
  max-height: 300px;
}

/* Stats Cards */
.text-xs {
  font-size: 0.7rem;
}

.font-weight-bold {
  font-weight: 700 !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-primary {
  color: #4e73df !important;
}

.text-success {
  color: #1cc88a !important;
}

.text-info {
  color: #36b9cc !important;
}

.text-warning {
  color: #f6c23e !important;
}

.text-danger {
  color: #e74a3b !important;
}

.text-gray-800 {
  color: #5a5c69 !important;
}

.text-gray-300 {
  color: #dddfeb !important;
}

/* Loading Spinner */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Tables */
.table-responsive {
  max-height: 300px;
  overflow-y: auto;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #5a5c69;
  font-size: 0.85rem;
}

.table td {
  font-size: 0.85rem;
  vertical-align: middle;
}

/* Badges */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

/* Empty State */
.text-center.text-muted {
  color: #858796 !important;
}

.text-center.text-muted i {
  color: #d1d3e2 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 1rem;
    text-align: center;
  }

  .chart-container {
    height: 250px;
  }

  .card-body {
    padding: 1rem;
  }
}

/* Animation for refresh button */
.fa-spin {
  animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}

/* Custom scrollbar for tables */
.table-responsive::-webkit-scrollbar {
  width: 6px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Card hover effects */
.card-body:hover .fa-2x {
  transform: scale(1.1);
  transition: transform 0.2s ease-in-out;
}

/* Alert styles */
.alert {
  border: none;
  border-radius: 10px;
}

/* Button styles */
.btn {
  border-radius: 8px;
  font-weight: 500;
}

.btn:disabled {
  opacity: 0.6;
}

/* Shadow utilities */
.shadow {
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}