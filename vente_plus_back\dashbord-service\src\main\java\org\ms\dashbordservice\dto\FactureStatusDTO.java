package org.ms.dashbordservice.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FactureStatusDTO {
    private Long factureId;
    private Date dateFacture;
    private BigDecimal montantTTC;
    private BigDecimal montantPaye;
    private BigDecimal montantRestant;
    private boolean estCompletelyPayee;
    private Long clientId;
    private String nomClient;
}
