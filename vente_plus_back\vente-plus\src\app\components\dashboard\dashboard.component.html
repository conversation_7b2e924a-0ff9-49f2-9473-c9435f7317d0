<!-- Dashboard Header -->
<div class="dashboard-header">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">
      <i class="fas fa-tachometer-alt me-2"></i>
      Tableau de Bord
    </h1>
    <button class="btn btn-primary" (click)="refreshData()" [disabled]="loading">
      <i class="fas fa-sync-alt" [class.fa-spin]="loading"></i>
      Actualiser
    </button>
  </div>
</div>

<!-- Loading State -->
<div *ngIf="loading" class="text-center py-5">
  <div class="spinner-border text-primary" role="status">
    <span class="visually-hidden">Chargement...</span>
  </div>
  <p class="mt-3">Chargement des données...</p>
</div>

<!-- Error State -->
<div *ngIf="error" class="alert alert-danger" role="alert">
  <i class="fas fa-exclamation-triangle me-2"></i>
  {{ error }}
</div>

<!-- Dashboard Content -->
<div *ngIf="!loading && !error">

  <!-- Stats Cards Row -->
  <div class="row mb-4">
    <!-- Total Revenue Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-primary shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                Chiffre d'Affaires Total
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ formatCurrency(stats.totalRevenue) }}
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-euro-sign fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Total Clients Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-success shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                Total Clients
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ stats.totalClients }}
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-users fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Total Invoices Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-info shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                Total Factures
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ stats.totalInvoices }}
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Total Debts Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-warning shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                Dettes Totales
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ formatCurrency(stats.totalDebts) }}
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts Row -->
  <div class="row mb-4">
    <!-- Revenue Chart -->
    <div class="col-xl-6 col-lg-6">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-primary">Chiffre d'Affaires par Client</h6>
        </div>
        <div class="card-body">
          <div class="chart-container">
            <canvas id="revenueChart" width="400" height="200"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Product Chart -->
    <div class="col-xl-6 col-lg-6">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-primary">Produits les Plus Vendus</h6>
        </div>
        <div class="card-body">
          <div class="chart-container">
            <canvas id="productChart" width="400" height="200"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Second Charts Row -->
  <div class="row mb-4">
    <!-- Payment Status Chart -->
    <div class="col-xl-6 col-lg-6">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-primary">Statut des Factures</h6>
        </div>
        <div class="card-body">
          <div class="chart-container">
            <canvas id="paymentChart" width="400" height="200"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Loyal Clients Chart -->
    <div class="col-xl-6 col-lg-6">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-primary">Clients les Plus Fidèles</h6>
        </div>
        <div class="card-body">
          <div class="chart-container">
            <canvas id="clientChart" width="400" height="200"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Data Tables Row -->
  <div class="row">
    <!-- Out of Stock Products -->
    <div class="col-xl-6 col-lg-6">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-danger">Produits en Rupture de Stock</h6>
          <span class="badge bg-danger">{{ outOfStockProducts.length }}</span>
        </div>
        <div class="card-body">
          <div *ngIf="outOfStockProducts.length === 0" class="text-center text-muted py-3">
            <i class="fas fa-check-circle fa-3x mb-3"></i>
            <p>Aucun produit en rupture de stock</p>
          </div>
          <div *ngIf="outOfStockProducts.length > 0" class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Produit</th>
                  <th>Référence</th>
                  <th>Prix</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let product of outOfStockProducts">
                  <td>{{ product.nom }}</td>
                  <td>{{ product.reference }}</td>
                  <td>{{ formatCurrency(product.prix) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Client Debts -->
    <div class="col-xl-6 col-lg-6">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-warning">Dettes Clients</h6>
          <span class="badge bg-warning">{{ Object.keys(clientDebts).length }}</span>
        </div>
        <div class="card-body">
          <div *ngIf="Object.keys(clientDebts).length === 0" class="text-center text-muted py-3">
            <i class="fas fa-check-circle fa-3x mb-3"></i>
            <p>Aucune dette client</p>
          </div>
          <div *ngIf="Object.keys(clientDebts).length > 0" class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Client</th>
                  <th>Montant Dû</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let debt of Object.keys(clientDebts)">
                  <td>{{ getClientName(debt) }}</td>
                  <td class="text-danger font-weight-bold">{{ formatCurrency(clientDebts[debt]) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Unpaid and Partial Invoices Row -->
  <div class="row">
    <!-- Unpaid Invoices -->
    <div class="col-xl-6 col-lg-6">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-danger">Factures Impayées</h6>
          <span class="badge bg-danger">{{ unpaidInvoices.length }}</span>
        </div>
        <div class="card-body">
          <div *ngIf="unpaidInvoices.length === 0" class="text-center text-muted py-3">
            <i class="fas fa-check-circle fa-3x mb-3"></i>
            <p>Aucune facture impayée</p>
          </div>
          <div *ngIf="unpaidInvoices.length > 0" class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Facture #</th>
                  <th>Client</th>
                  <th>Montant</th>
                  <th>Date</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let invoice of unpaidInvoices">
                  <td>{{ invoice.facture.id }}</td>
                  <td>{{ invoice.client.prenom }} {{ invoice.client.nom }}</td>
                  <td class="text-danger">{{ formatCurrency(invoice.facture.montantTTC) }}</td>
                  <td>{{ invoice.facture.dateFacture | date:'short' }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Partially Paid Invoices -->
    <div class="col-xl-6 col-lg-6">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-warning">Factures Partiellement Payées</h6>
          <span class="badge bg-warning">{{ partiallyPaidInvoices.length }}</span>
        </div>
        <div class="card-body">
          <div *ngIf="partiallyPaidInvoices.length === 0" class="text-center text-muted py-3">
            <i class="fas fa-check-circle fa-3x mb-3"></i>
            <p>Aucune facture partiellement payée</p>
          </div>
          <div *ngIf="partiallyPaidInvoices.length > 0" class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Facture #</th>
                  <th>Client</th>
                  <th>Payé</th>
                  <th>Restant</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let invoice of partiallyPaidInvoices">
                  <td>{{ invoice.facture.id }}</td>
                  <td>{{ invoice.client.prenom }} {{ invoice.client.nom }}</td>
                  <td class="text-success">{{ formatCurrency(invoice.montantPaye) }}</td>
                  <td class="text-warning">{{ formatCurrency(invoice.montantRestant) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>
